/* History Page Enhancements */

.page {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered animation for history items */
app-generic-history-row {
    animation: slideInUp 0.4s ease-out;
    animation-fill-mode: both;
}

app-generic-history-row:nth-child(1) { animation-delay: 0.1s; }
app-generic-history-row:nth-child(2) { animation-delay: 0.2s; }
app-generic-history-row:nth-child(3) { animation-delay: 0.3s; }
app-generic-history-row:nth-child(4) { animation-delay: 0.4s; }
app-generic-history-row:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced empty state */
.text-center svg {
    transition: transform 0.3s ease-in-out;
}

.text-center:hover svg {
    transform: scale(1.05);
}

/* Section headers with subtle background */
h2 {
    position: relative;
    padding: 0.5rem 0;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    border-radius: 1px;
}