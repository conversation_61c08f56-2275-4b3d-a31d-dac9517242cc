import { Component } from '@angular/core';
import { HISTORY_TABS, SelectedHistoryTab } from '../../constants/tab-definitions';
import { TabItem } from '../../components/generic-tabbar/generic-tabbar.component';
import { HistoryRowData } from '../../components/history-row/generic-history-row.component';
import { OrderStatus } from '../../models/order.dto';

@Component({
  selector: 'app-history',
  templateUrl: './history.component.html',
  styleUrl: './history.component.css'
})
export class HistoryComponent {
  public selectedTab: SelectedHistoryTab = 'ORDERS';
  public tabs: TabItem[] = HISTORY_TABS;

  public orderHistoryData: HistoryRowData[] = [
    {
      primaryId: '1234567890',
      primaryLabel: 'شماره سفارش',
      timestamp: Date.now(),
      amount: '36.789.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'موفق',
        colorClass: 'text-emerald-600 dark:text-emerald-400'
      },
      tertiaryInfo: {
        label: 'نوع شارژ',
        value: 'شارژ نقدی'
      },
      quaternaryInfo: {
        label: 'مبلغ شارژ',
        value: '36.789.000'
      },
      status: 'PAYMENT_SUCCESS' as OrderStatus,
      statusText: {
        success: 'شارژ با موفقیت انجام شد',
        failure: 'شارژ ناموفق'
      },
      showDiscount: true,
      discountPercent: 15
    },
    {
      primaryId: '1234567891',
      primaryLabel: 'شماره سفارش',
      timestamp: Date.now() - 3600000, // 1 hour ago
      amount: '25.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'در انتظار',
        colorClass: 'text-amber-600 dark:text-amber-400'
      },
      tertiaryInfo: {
        label: 'نوع شارژ',
        value: 'شارژ اعتباری'
      },
      quaternaryInfo: {
        label: 'مبلغ شارژ',
        value: '25.000.000'
      },
      status: 'PAYMENT_PENDING' as OrderStatus,
      statusText: {
        success: 'شارژ با موفقیت انجام شد',
        failure: 'شارژ در انتظار پردازش'
      }
    },
    {
      primaryId: '1234567892',
      primaryLabel: 'شماره سفارش',
      timestamp: Date.now() - 7200000, // 2 hours ago
      amount: '50.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'ناموفق',
        colorClass: 'text-red-600 dark:text-red-400'
      },
      tertiaryInfo: {
        label: 'نوع شارژ',
        value: 'شارژ نقدی'
      },
      quaternaryInfo: {
        label: 'علت عدم موفقیت',
        value: 'موجودی ناکافی'
      },
      status: 'PAYMENT_FAILED' as OrderStatus,
      statusText: {
        success: 'شارژ با موفقیت انجام شد',
        failure: 'شارژ به دلیل موجودی ناکافی ناموفق بود'
      }
    },
    {
      primaryId: '1234567893',
      primaryLabel: 'شماره سفارش',
      timestamp: Date.now() - 10800000, // 3 hours ago
      amount: '15.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'لغو شده',
        colorClass: 'text-gray-600 dark:text-gray-400'
      },
      tertiaryInfo: {
        label: 'نوع شارژ',
        value: 'شارژ اعتباری'
      },
      quaternaryInfo: {
        label: 'علت لغو',
        value: 'لغو توسط کاربر'
      },
      status: 'USER_CANCELED' as OrderStatus,
      statusText: {
        success: 'شارژ با موفقیت انجام شد',
        failure: 'سفارش توسط کاربر لغو شد'
      }
    },
    {
      primaryId: '1234567894',
      primaryLabel: 'شماره سفارش',
      timestamp: Date.now() - 14400000, // 4 hours ago
      amount: '100.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'موفق',
        colorClass: 'text-emerald-600 dark:text-emerald-400'
      },
      tertiaryInfo: {
        label: 'نوع شارژ',
        value: 'شارژ نقدی'
      },
      quaternaryInfo: {
        label: 'مبلغ شارژ',
        value: '100.000.000'
      },
      status: 'FULFILLED' as OrderStatus,
      statusText: {
        success: 'شارژ با موفقیت انجام و تحویل داده شد',
        failure: 'شارژ ناموفق'
      },
      showDiscount: true,
      discountPercent: 10
    }
  ];

  public paymentHistoryData: HistoryRowData[] = [
    {
      primaryId: 'TXN001234567890',
      primaryLabel: 'شماره تراکنش',
      timestamp: Date.now(),
      amount: '36.789.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'موفق',
        colorClass: 'text-emerald-600 dark:text-emerald-400'
      },
      tertiaryInfo: {
        label: 'نوع تراکنش',
        value: 'پرداخت'
      },
      status: 'PAYMENT_SUCCESS' as OrderStatus,
      statusText: {
        success: 'تراکنش با موفقیت انجام شد',
        failure: 'تراکنش ناموفق'
      }
    },
    {
      primaryId: 'TXN001234567891',
      primaryLabel: 'شماره تراکنش',
      timestamp: Date.now() - 1800000, // 30 minutes ago
      amount: '12.500.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'در انتظار',
        colorClass: 'text-amber-600 dark:text-amber-400'
      },
      tertiaryInfo: {
        label: 'نوع تراکنش',
        value: 'واریز'
      },
      status: 'PAYMENT_PENDING' as OrderStatus,
      statusText: {
        success: 'تراکنش با موفقیت انجام شد',
        failure: 'تراکنش در انتظار تایید بانک'
      }
    },
    {
      primaryId: 'TXN001234567892',
      primaryLabel: 'شماره تراکنش',
      timestamp: Date.now() - 5400000, // 1.5 hours ago
      amount: '75.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'ناموفق',
        colorClass: 'text-red-600 dark:text-red-400'
      },
      tertiaryInfo: {
        label: 'نوع تراکنش',
        value: 'پرداخت'
      },
      quaternaryInfo: {
        label: 'علت عدم موفقیت',
        value: 'خطای شبکه بانکی'
      },
      status: 'PAYMENT_FAILED' as OrderStatus,
      statusText: {
        success: 'تراکنش با موفقیت انجام شد',
        failure: 'تراکنش به دلیل خطای شبکه ناموفق بود'
      }
    },
    {
      primaryId: 'TXN001234567893',
      primaryLabel: 'شماره تراکنش',
      timestamp: Date.now() - 9000000, // 2.5 hours ago
      amount: '200.000.000',
      currency: 'ریال',
      secondaryInfo: {
        label: 'وضعیت',
        value: 'لغو شده',
        colorClass: 'text-gray-600 dark:text-gray-400'
      },
      tertiaryInfo: {
        label: 'نوع تراکنش',
        value: 'برداشت'
      },
      quaternaryInfo: {
        label: 'علت لغو',
        value: 'درخواست کاربر'
      },
      status: 'PAYMENT_CANCELED' as OrderStatus,
      statusText: {
        success: 'تراکنش با موفقیت انجام شد',
        failure: 'تراکنش توسط کاربر لغو شد'
      }
    }
  ];

  public onTabChange(tab: string): void {
    this.selectedTab = tab as SelectedHistoryTab;
  }
}
