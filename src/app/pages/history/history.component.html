<div class="page">
    <!-- Page Header -->
    <div class="mt-8 mb-4 mx-2">
        <h1 class="text-2xl font-bold text-slate-800 dark:text-slate-100 text-center mb-2"
            style="font-family: 'yekan-b'">
            تاریخچه تراکنش‌ها
        </h1>
        <p class="text-sm text-slate-600 dark:text-slate-400 text-center">
            مشاهده تاریخچه سفارشات و پرداخت‌های شما
        </p>
    </div>

    <!-- Tab Navigation -->
    <div class="flex items-center justify-center w-full mb-6">
        <app-generic-tabbar [tabs]="tabs" [defaultTab]="'ORDERS'" (onTabChange)="onTabChange($event)">
        </app-generic-tabbar>
    </div>

    <!-- Content Section -->
    <div class="space-y-4">
        <!-- Orders Tab -->
        @if (selectedTab === 'ORDERS') {
        <div class="space-y-4">
            <div class="flex flex-row-reverse items-center justify-between mb-4 px-3">
                <h2 class="text-lg font-semibold text-slate-700 dark:text-slate-300" style="font-family: 'yekan-b'">
                    سفارشات شما
                </h2>
                <span class="text-sm text-slate-500 dark:text-slate-400">
                    {{ orderHistoryData.length }} سفارش
                </span>
            </div>
            <div class="grid grid-cols-1 gap-4">

                @for (orderData of orderHistoryData; track orderData.primaryId) {
                <app-generic-history-row [rowType]="'order'" [data]="orderData"></app-generic-history-row>
                }
                @empty {
                <div class="text-center py-12">
                    <div class="text-slate-400 dark:text-slate-500 mb-2">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9zm1-5a1 1 0 100 2 1 1 0 000-2z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">هیچ سفارشی یافت نشد</h3>
                    <p class="text-sm text-slate-500 dark:text-slate-500">هنوز هیچ سفارشی ثبت نکرده‌اید</p>
                </div>
                }
            </div>
        </div>
        } @else {
        <!-- Payments Tab -->
        <div class="space-y-4">
            <div class="flex flex-row-reverse items-center justify-between mb-4 px-3">
                <h2 class="text-lg font-semibold text-slate-700 dark:text-slate-300" style="font-family: 'yekan-b'">
                    تراکنش‌های شما
                </h2>
                <span class="text-sm text-slate-500 dark:text-slate-400">
                    {{ paymentHistoryData.length }} تراکنش
                </span>
            </div>
            <div class="grid grid-cols-1 gap-4">
                @for (paymentData of paymentHistoryData; track paymentData.primaryId) {
                <app-generic-history-row [rowType]="'payment'" [data]="paymentData"></app-generic-history-row>
                }
                @empty {
                <div class="text-center py-12">
                    <div class="text-slate-400 dark:text-slate-500 mb-2">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">هیچ تراکنشی یافت نشد</h3>
                    <p class="text-sm text-slate-500 dark:text-slate-500">هنوز هیچ تراکنشی انجام نداده‌اید</p>
                </div>
                }
            </div>
        </div>
        }
    </div>
</div>