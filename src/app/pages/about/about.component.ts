import { Component } from '@angular/core';
import { ABOUT_TABS, SelectedAboutTab } from '../../constants/tab-definitions';
import { TabItem } from '../../components/generic-tabbar/generic-tabbar.component';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrl: './about.component.css'
})
export class AboutComponent {
  public selectedTab: SelectedAboutTab = 'ABOUT_US';
  public tabs: TabItem[] = ABOUT_TABS;

  public onTabChange(tab: string): void {
    this.selectedTab = tab as SelectedAboutTab;
  }
}
