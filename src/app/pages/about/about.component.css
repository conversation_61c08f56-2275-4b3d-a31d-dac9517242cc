/* Content Container */
.content-container {
    @apply px-3 pb-6;
}

/* About Content Styles */
.about-content {
    @apply space-y-8;
}

.about-card .header {
    @apply text-center;
    direction: rtl;
}

.about-card .content {
    @apply text-justify;
    direction: rtl;
}

/* Hero Section */
.hero-section {
    @apply text-center py-8 px-4;
    @apply bg-gradient-to-br from-amber-50 to-orange-50;
    @apply dark:from-slate-800 dark:to-slate-900;
    @apply rounded-2xl mx-3 mb-6;
    @apply ring-1 ring-amber-100 dark:ring-slate-700;
}

.hero-icon {
    @apply text-amber-500 dark:text-amber-400 mb-4;
    @apply flex justify-center;
}

.hero-title {
    @apply text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2;
    @apply text-center;
    font-family: "yekan-b";
}

.hero-subtitle {
    @apply text-lg text-slate-600 dark:text-slate-300;
    @apply max-w-md mx-auto text-center;
}

/* About Cards */
.about-cards {
    @apply space-y-4 px-3;
}

.about-card {
    @apply bg-white dark:bg-slate-800 rounded-xl p-6;
    @apply ring-1 ring-slate-200 dark:ring-slate-700;
    @apply shadow-lg shadow-slate-200/50 dark:shadow-slate-900/50;
    @apply transition-all duration-300;
    @apply hover:shadow-xl hover:shadow-slate-300/50 dark:hover:shadow-slate-900/70;
    @apply hover:ring-amber-200 dark:hover:ring-amber-600/30;
}

.card-icon {
    @apply text-amber-500 dark:text-amber-400 mb-4;
    @apply bg-amber-50 dark:bg-amber-900/20;
    @apply w-16 h-16 rounded-full flex items-center justify-center mx-auto;
}

.about-card h3 {
    @apply text-xl font-bold text-slate-800 dark:text-slate-100 mb-3;
    @apply text-right;
    font-family: "yekan-b";
}

.about-card p {
    @apply text-slate-600 dark:text-slate-300 leading-relaxed;
    @apply text-justify;
}

/* Features Section */
.features-section {
    @apply px-3;
}

.section-title {
    @apply text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6;
    @apply text-center;
    font-family: "yekan-b";
}

.features-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4;
}

.feature-item {
    @apply bg-white dark:bg-slate-800 rounded-xl p-4;
    @apply ring-1 ring-slate-200 dark:ring-slate-700;
    @apply shadow-md shadow-slate-200/50 dark:shadow-slate-900/50;
    @apply text-center;
    @apply transition-all duration-300;
    @apply hover:shadow-lg hover:shadow-slate-300/50 dark:hover:shadow-slate-900/70;
    @apply hover:ring-amber-200 dark:hover:ring-amber-600/30;
}

.feature-icon {
    @apply text-amber-500 dark:text-amber-400 mb-3;
    @apply bg-amber-50 dark:bg-amber-900/20;
    @apply w-12 h-12 rounded-full flex items-center justify-center mx-auto;
}

.feature-item h4 {
    @apply text-lg font-bold text-slate-800 dark:text-slate-100 mb-2;
    @apply text-center;
    font-family: "yekan-b";
}

.feature-item p {
    @apply text-sm text-slate-600 dark:text-slate-300;
    @apply text-center;
}

/* Contact Section */
.contact-section {
    @apply px-3;
}

.contact-info {
    @apply space-y-4;
}

.contact-item {
    @apply bg-white dark:bg-slate-800 rounded-xl p-4;
    @apply ring-1 ring-slate-200 dark:ring-slate-700;
    @apply shadow-md shadow-slate-200/50 dark:shadow-slate-900/50;
    @apply flex space-x-4 items-center;
}

.contact-text {
    @apply text-right flex-grow;
}

.contact-icon {
    @apply text-amber-500 dark:text-amber-400;
    @apply bg-amber-50 dark:bg-amber-900/20;
    @apply w-12 h-12 rounded-full flex items-center justify-center;
    @apply flex-shrink-0;
}

.contact-item h5 {
    @apply text-lg font-bold text-slate-800 dark:text-slate-100 mb-1;
    @apply text-right;
    font-family: "yekan-b";
}

.contact-value {
    @apply text-slate-600 dark:text-slate-300;
    @apply font-sans tracking-wider text-right;
}

/* Terms & Conditions Styles */
.terms-content {
    @apply space-y-8;
}

/* Terms Header */
.terms-header {
    @apply text-center py-8 px-4;
    @apply bg-gradient-to-br from-indigo-50 to-blue-50;
    @apply dark:from-slate-800 dark:to-slate-900;
    @apply rounded-2xl mx-3 mb-6;
    @apply ring-1 ring-indigo-100 dark:ring-slate-700;
}

.terms-icon {
    @apply text-rose-500 dark:text-rose-400 mb-4;
    @apply flex justify-center;
}

.terms-title {
    @apply text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2;
    @apply text-center;
    font-family: "yekan-b";
}

.terms-subtitle {
    @apply text-sm text-slate-500 dark:text-slate-400;
    @apply bg-slate-100 dark:bg-slate-700;
    @apply px-3 py-1 rounded-full inline-block;
}

/* Terms Sections */
.terms-sections {
    @apply space-y-6 px-3;
}

.terms-section .header, .terms-section .content {
    @apply text-justify;
    direction: rtl;
}

.terms-section {
    @apply bg-white dark:bg-slate-800 rounded-xl p-6;
    @apply ring-1 ring-slate-200 dark:ring-slate-700;
    @apply shadow-lg shadow-slate-200/50 dark:shadow-slate-900/50;
}

.terms-section h2 {
    @apply text-xl font-bold text-slate-800 dark:text-slate-100 mb-4;
    @apply border-b border-slate-200 dark:border-slate-700 pb-2;
    @apply text-right;
    font-family: "yekan-b";
}

.terms-section p {
    @apply text-slate-600 dark:text-slate-300 leading-relaxed mb-4;
    @apply text-justify;
}

.terms-section p:last-child {
    @apply mb-0;
}

/* Terms List */
.terms-list {
    @apply list-none space-y-2 ml-4;
}

.terms-list li {
    @apply text-slate-600 dark:text-slate-300;
    @apply flex flex-row-reverse items-start space-x-reverse space-x-3;
    @apply text-justify;
}

.terms-list li::before {
    content: "•";
    @apply text-amber-500 dark:text-amber-400 font-bold text-lg;
    @apply flex-shrink-0 mt-0.5;
}

/* Highlight Boxes */
.highlight-box {
    @apply bg-green-50 dark:bg-green-900/20;
    @apply border border-green-200 dark:border-green-700;
    @apply rounded-lg p-4 my-4;
    @apply flex space-x-3 items-start;
}

.highlight-box p {
    @apply text-green-800 dark:text-green-200 mb-0;
    @apply text-right flex-grow;
}

.highlight-icon {
    @apply text-green-500 dark:text-green-400;
}

.warning-box {
    @apply bg-amber-50 dark:bg-amber-900/20;
    @apply border border-amber-200 dark:border-amber-700;
    @apply rounded-lg p-4 my-4;
    @apply flex space-x-3 items-start;
}

.warning-box p {
    @apply text-amber-800 dark:text-amber-200 mb-0;
    @apply text-right flex-grow;
}

.warning-icon {
    @apply text-amber-500 dark:text-amber-400;
}

/* Responsive Design */
@media (max-width: 640px) {
    .hero-title {
        @apply text-2xl;
    }

    .hero-subtitle {
        @apply text-base;
    }

    .section-title {
        @apply text-xl;
    }

    .about-card {
        @apply p-4;
    }

    .terms-section {
        @apply p-4;
    }

    .features-grid {
        @apply grid-cols-1;
    }
}

/* Animation and Transitions */
.about-card,
.feature-item,
.contact-item,
.terms-section {
    @apply transform transition-all duration-300 ease-in-out;
}

.about-card:hover,
.feature-item:hover {
    @apply -translate-y-1;
}

/* Smooth scrolling for long content */
.terms-content {
    scroll-behavior: smooth;
}

/* Typography enhancements */
.about-content h1,
.about-content h2,
.about-content h3,
.about-content h4,
.terms-content h1,
.terms-content h2 {
    @apply text-balance;
}

.about-content p,
.terms-content p {
    @apply text-pretty;
}

/* Loading animation for content */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.about-card,
.feature-item,
.contact-item,
.terms-section {
    animation: fadeInUp 0.6s ease-out forwards;
}

.about-card:nth-child(1) { animation-delay: 0.1s; }
.about-card:nth-child(2) { animation-delay: 0.2s; }
.about-card:nth-child(3) { animation-delay: 0.3s; }

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }



/* Improved text contrast */
.about-card p,
.terms-section p {
    @apply text-slate-700 dark:text-slate-200;
}

/* Better spacing for mobile */
@media (max-width: 480px) {
    .content-container {
        @apply px-2;
    }

    .hero-section,
    .terms-header {
        @apply mx-2 px-3 py-6;
    }

    .about-cards,
    .features-section,
    .contact-section,
    .terms-sections {
        @apply px-2;
    }

    .about-card,
    .terms-section {
        @apply p-3;
    }

    .card-icon {
        @apply w-12 h-12;
    }

    .feature-icon {
        @apply w-10 h-10;
    }
}