# About Page Component

## Overview
A comprehensive About Us page with two tabs: "درباره ما" (About Us) and "قوانین و مقررات" (Terms & Conditions), designed following the project's design language and modern UI/UX principles.

## Features

### Design Language Compliance
- Follows the existing project's design patterns
- Uses consistent color scheme (amber primary, slate backgrounds)
- Implements the same tabbar pattern as other pages
- Maintains RTL (Right-to-Left) layout for Persian content
  - Proper RTL direction with `direction: rtl` on main container
  - Uses `flex-row-reverse` and `space-x-reverse` for correct element ordering
  - Text alignment with `text-right` for headings and `text-justify` for paragraphs
  - Icon and text positioning follows RTL conventions
- Uses project's typography system (Yekan font family)

### About Us Tab
- **Hero Section**: Brand introduction with gradient background
- **Mission Cards**: Three cards showcasing mission, vision, and values
- **Features Grid**: Key features in a responsive grid layout
- **Contact Information**: Email and phone contact details

### Terms & Conditions Tab
- **Structured Content**: Well-organized sections with numbering
- **Highlight Boxes**: Important information with colored backgrounds
- **Warning Boxes**: Critical notices with appropriate styling
- **Typography**: Proper heading hierarchy and readable text

### UI/UX Features
- **Responsive Design**: Mobile-first approach with breakpoints
- **Dark Mode Support**: Full dark/light theme compatibility
- **Smooth Animations**: Fade-in animations and hover effects
- **Accessibility**: Focus states and proper contrast ratios
- **Persian Lorem Ipsum**: Authentic Persian placeholder content

## Technical Implementation

### Component Structure
```
about/
├── about.component.ts    # Component logic with tab management
├── about.component.html  # Template with conditional content
├── about.component.css   # Comprehensive styling
└── README.md            # This documentation
```

### Key Classes
- `.about-tabbar`: Tab navigation styling
- `.hero-section`: Main brand introduction area
- `.about-cards`: Mission, vision, values cards
- `.features-grid`: Key features layout
- `.terms-sections`: Terms content organization

### Responsive Breakpoints
- Mobile: < 480px (enhanced mobile experience)
- Small: < 640px (tablet adjustments)
- Default: >= 640px (desktop layout)

## Content Structure

### Persian Content
All content uses authentic Persian Lorem Ipsum text that maintains the natural flow and readability of the Persian language, avoiding direct translations that might feel awkward.

### Accessibility
- Semantic HTML structure
- Proper heading hierarchy (h1, h2, h3, h4)
- Focus indicators for keyboard navigation
- High contrast ratios for text readability
- Screen reader friendly markup

## Usage
The component is already integrated into the app routing system and can be accessed via `/about` route. The tab functionality allows users to switch between "About Us" and "Terms & Conditions" content seamlessly.
