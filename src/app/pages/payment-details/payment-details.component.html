<div class="page">
    <!-- Loading State -->
    @if (isLoading) {
    <div class="loading-center">
        <div class="spinner-base"></div>
        <p class="text-slate-600 dark:text-slate-400">در حال بارگذاری اطلاعات پرداخت...</p>
    </div>
    }

    <!-- Error State -->
    @else if (error) {
    <div class="content-card mx-3 my-6">
        <div class="text-center py-8">
            <div class="text-red-500 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">خطا در بارگذاری</h3>
            <p class="text-sm text-slate-500 dark:text-slate-500 mb-4">{{ error }}</p>
            <button (click)="retryLoadPayment()"
                    class="px-4 py-2 bg-amber-500 text-slate-900 rounded-lg font-medium hover:bg-amber-400 transition-colors">
                تلاش مجدد
            </button>
        </div>
    </div>
    }

    <!-- Success State -->
    @else if (paymentDetails) {
    <div class="space-y-4">
        <!-- Header -->
        <div class="flex items-center justify-between mx-3 mt-6 mb-4">
            <button (click)="goBack()" 
                    class="flex items-center text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                بازگشت
            </button>
            <h1 class="text-xl font-bold text-slate-800 dark:text-slate-100" style="font-family: 'yekan-b'">
                جزئیات پرداخت
            </h1>
            <div class="w-16"></div> <!-- Spacer for centering -->
        </div>

        <!-- Payment Details Card -->
        <div class="content-card">
            <div class="space-y-4">
                <div class="flex items-center justify-between pb-3 border-b border-slate-200 dark:border-slate-700">
                    <h2 class="text-lg font-semibold text-slate-700 dark:text-slate-300" style="font-family: 'yekan-b'">
                        اطلاعات پرداخت
                    </h2>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="px-3 py-1 rounded-full text-xs font-medium"
                              [ngClass]="getStatusColorClass()">
                            {{ paymentDetails.statusText }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">شناسه پرداخت:</span>
                            <span class="font-mono text-sm text-slate-700 dark:text-slate-300">{{ paymentDetails.paymentId }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">مبلغ پرداخت:</span>
                            <span class="font-mono text-lg font-bold text-slate-800 dark:text-slate-200">
                                {{ paymentDetails.amount }} {{ paymentDetails.currency }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">تاریخ و زمان:</span>
                            <span class="text-sm text-slate-700 dark:text-slate-300">{{ paymentDetails.timestamp | date:'yyyy/MM/dd HH:mm' }}</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">روش پرداخت:</span>
                            <span class="text-sm text-slate-700 dark:text-slate-300">{{ paymentDetails.paymentMethod }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">شماره مرجع:</span>
                            <span class="font-mono text-sm text-slate-700 dark:text-slate-300">{{ paymentDetails.referenceNumber }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-500 dark:text-slate-400">شماره کارت:</span>
                            <span class="font-mono text-sm text-slate-700 dark:text-slate-300">{{ paymentDetails.cardNumber }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Section -->
        @if (isLoadingInvoice) {
        <div class="content-card">
            <div class="loading-center py-8">
                <div class="spinner-base"></div>
                <p class="text-slate-600 dark:text-slate-400">در حال بارگذاری فاکتور...</p>
            </div>
        </div>
        } @else if (invoiceError) {
        <div class="content-card">
            <div class="text-center py-8">
                <div class="text-amber-500 mb-4">
                    <svg class="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-slate-600 dark:text-slate-400 mb-2">خطا در بارگذاری فاکتور</h3>
                <p class="text-sm text-slate-500 dark:text-slate-500 mb-4">{{ invoiceError }}</p>
                <button (click)="retryLoadInvoice()"
                        class="px-4 py-2 bg-amber-500 text-slate-900 rounded-lg font-medium hover:bg-amber-400 transition-colors">
                    تلاش مجدد
                </button>
            </div>
        </div>
        } @else if (invoice) {
        <div class="content-card">
            <div class="space-y-4">
                <div class="flex items-center justify-between pb-3 border-b border-slate-200 dark:border-slate-700">
                    <h2 class="text-lg font-semibold text-slate-700 dark:text-slate-300" style="font-family: 'yekan-b'">
                        فاکتور پرداخت
                    </h2>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button (click)="printInvoice()" 
                                class="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
                                title="چاپ فاکتور">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                        </button>
                        <button (click)="downloadInvoice()" 
                                class="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
                                title="دانلود فاکتور">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Invoice Content -->
                <div class="invoice-container">
                    <!-- Invoice Header -->
                    <div class="invoice-header">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-4 space-x-reverse">
                                @if (invoice.shopLogo) {
                                <img [src]="invoice.shopLogo" [alt]="invoice.shopName" class="w-16 h-16 rounded-lg object-cover">
                                }
                                <div>
                                    <h3 class="text-xl font-bold text-slate-800 dark:text-slate-100" style="font-family: 'yekan-b'">
                                        {{ invoice.shopName }}
                                    </h3>
                                    <p class="text-sm text-slate-600 dark:text-slate-400">فاکتور پرداخت</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="text-sm text-slate-500 dark:text-slate-400 mb-1">شماره صورتحساب:</div>
                                <div class="font-mono text-lg font-bold text-slate-800 dark:text-slate-200">{{ invoice.invoiceNumber }}</div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div>
                                <div class="text-sm text-slate-500 dark:text-slate-400 mb-1">شناسه پرداخت:</div>
                                <div class="font-mono text-sm text-slate-700 dark:text-slate-300">{{ invoice.paymentId }}</div>
                            </div>
                            <div>
                                <div class="text-sm text-slate-500 dark:text-slate-400 mb-1">تاریخ و زمان پرداخت:</div>
                                <div class="text-sm text-slate-700 dark:text-slate-300">{{ invoice.paymentDateTime | date:'yyyy/MM/dd HH:mm' }}</div>
                            </div>
                            <div>
                                <div class="text-sm text-slate-500 dark:text-slate-400 mb-1">شماره صندوق:</div>
                                <div class="font-mono text-sm text-slate-700 dark:text-slate-300">{{ invoice.cashRegisterNumber }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items Table -->
                    <div class="invoice-table">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b-2 border-slate-300 dark:border-slate-600">
                                        <th class="text-right py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">ردیف</th>
                                        <th class="text-right py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">شرح</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">تعداد</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">قیمت واحد</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">قیمت کل</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">درصد تخفیف</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">ارزش افزوده</th>
                                        <th class="text-center py-3 px-2 text-sm font-bold text-slate-700 dark:text-slate-300">مبلغ نهایی</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (item of invoice.items; track item.id; let i = $index) {
                                    <tr class="border-b border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50">
                                        <td class="py-3 px-2 text-sm text-slate-600 dark:text-slate-400 text-center">{{ i + 1 }}</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300">
                                            <div class="whitespace-pre-line">{{ item.description }}</div>
                                        </td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono">{{ item.quantity }}</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono">{{ item.unitPrice | number }}</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono">{{ item.totalPrice | number }}</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono">{{ item.discountPercent }}%</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono">{{ item.taxAmount | number }}</td>
                                        <td class="py-3 px-2 text-sm text-slate-700 dark:text-slate-300 text-center font-mono font-bold">{{ item.finalAmount | number }}</td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mt-6">
                        <div class="flex justify-end">
                            <div class="w-full max-w-md space-y-3">
                                <div class="flex justify-between items-center py-2 border-b border-slate-200 dark:border-slate-700">
                                    <span class="text-sm text-slate-600 dark:text-slate-400">کل مبلغ تخفیف دریافتی:</span>
                                    <span class="font-mono text-sm font-bold text-emerald-600 dark:text-emerald-400">{{ invoice.summary.totalDiscount | number }} ریال</span>
                                </div>
                                <div class="flex justify-between items-center py-2 border-b border-slate-200 dark:border-slate-700">
                                    <span class="text-sm text-slate-600 dark:text-slate-400">کل مبلغ ارزش افزوده پرداختی:</span>
                                    <span class="font-mono text-sm font-bold text-slate-600 dark:text-slate-400">{{ invoice.summary.totalTax | number }} ریال</span>
                                </div>
                                <div class="flex justify-between items-center py-3 border-t-2 border-slate-300 dark:border-slate-600">
                                    <span class="text-lg font-bold text-slate-800 dark:text-slate-200" style="font-family: 'yekan-b'">کل مبلغ پرداختی:</span>
                                    <span class="font-mono text-xl font-bold text-slate-900 dark:text-slate-100">{{ invoice.summary.totalAmount | number }} ریال</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Daily Message -->
                    @if (invoice.dailyMessage) {
                    <div class="invoice-footer mt-8">
                        <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                            <div class="flex items-start space-x-3 space-x-reverse">
                                <div class="flex-shrink-0">
                                    <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">پیام روز</h4>
                                    <p class="text-sm text-amber-700 dark:text-amber-300 whitespace-pre-line">{{ invoice.dailyMessage }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    }
                </div>
            </div>
        </div>
        }
    </div>
    }
</div>
