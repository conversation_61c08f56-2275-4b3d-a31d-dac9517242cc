/* Invoice Container */
.invoice-container {
  @apply bg-white dark:bg-slate-900 rounded-lg;
  @apply border border-slate-200 dark:border-slate-700;
  @apply p-6;
}

/* Invoice Header */
.invoice-header {
  @apply border-b border-slate-200 dark:border-slate-700 pb-6 mb-6;
}

/* Invoice Table */
.invoice-table {
  @apply mb-6;
}

.invoice-table table {
  @apply border-collapse;
}

.invoice-table th {
  @apply bg-slate-100 dark:bg-slate-800;
  @apply font-bold text-xs uppercase tracking-wider;
  @apply sticky top-0 z-10;
}

.invoice-table td {
  @apply align-top;
  @apply min-h-[3rem];
}

/* Multi-line support for descriptions */
.invoice-table td .whitespace-pre-line {
  @apply leading-relaxed;
  @apply max-w-xs;
  word-wrap: break-word;
  white-space: pre-line;
}

/* Invoice Summary */
.invoice-summary {
  @apply border-t border-slate-200 dark:border-slate-700 pt-6;
}

/* Invoice Footer */
.invoice-footer {
  @apply border-t border-slate-200 dark:border-slate-700 pt-6;
}

/* Print Styles */
@media print {
  .page {
    @apply m-0 p-0;
  }
  
  .content-card {
    @apply shadow-none border-none m-0 p-0;
  }
  
  .invoice-container {
    @apply border-none shadow-none;
  }
  
  /* Hide non-essential elements when printing */
  button,
  .flex.items-center.justify-between.mx-3,
  .flex.items-center.justify-between.pb-3 button {
    @apply hidden;
  }
  
  /* Ensure proper page breaks */
  .invoice-table {
    page-break-inside: avoid;
  }
  
  .invoice-summary {
    page-break-inside: avoid;
  }
  
  /* Print-specific table styling */
  .invoice-table table {
    @apply w-full border-collapse;
  }
  
  .invoice-table th,
  .invoice-table td {
    @apply border border-slate-400 p-2;
  }
  
  .invoice-table th {
    @apply bg-slate-100 font-bold;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .invoice-table {
    @apply overflow-x-auto;
  }
  
  .invoice-table table {
    @apply min-w-full;
  }
  
  .invoice-table th,
  .invoice-table td {
    @apply px-1 py-2 text-xs;
  }
  
  .invoice-summary .max-w-md {
    @apply max-w-full;
  }
}

/* Enhanced hover effects */
.invoice-table tbody tr:hover {
  @apply bg-slate-50 dark:bg-slate-800/50;
  @apply transition-colors duration-200;
}

/* Status badge animations */
.px-3.py-1.rounded-full {
  @apply transition-all duration-200;
  @apply hover:scale-105;
}

/* Button hover effects */
button:hover svg {
  @apply transform scale-110 transition-transform duration-200;
}

/* Loading and error states */
.loading-center {
  @apply min-h-[50vh];
}

.spinner-base {
  @apply dark:border-slate-700 dark:border-t-amber-400;
}

/* Enhanced card styling */
.content-card {
  @apply transition-all duration-200;
  @apply hover:shadow-lg;
}

/* RTL-specific adjustments */
[dir="rtl"] .invoice-table th,
[dir="rtl"] .invoice-table td {
  @apply text-right;
}

[dir="rtl"] .invoice-table th:nth-child(3),
[dir="rtl"] .invoice-table th:nth-child(4),
[dir="rtl"] .invoice-table th:nth-child(5),
[dir="rtl"] .invoice-table th:nth-child(6),
[dir="rtl"] .invoice-table th:nth-child(7),
[dir="rtl"] .invoice-table th:nth-child(8),
[dir="rtl"] .invoice-table td:nth-child(3),
[dir="rtl"] .invoice-table td:nth-child(4),
[dir="rtl"] .invoice-table td:nth-child(5),
[dir="rtl"] .invoice-table td:nth-child(6),
[dir="rtl"] .invoice-table td:nth-child(7),
[dir="rtl"] .invoice-table td:nth-child(8) {
  @apply text-center;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .invoice-container {
    @apply ring-1 ring-slate-700;
  }
  
  .invoice-table th {
    @apply bg-slate-800 text-slate-200;
  }
  
  .invoice-table tbody tr:hover {
    @apply bg-slate-800/70;
  }
}
