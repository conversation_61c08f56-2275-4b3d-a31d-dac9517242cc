import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, finalize } from 'rxjs';
import { PaymentDetailsService } from '../../services/payment-details.service';
import { PaymentDetails, Invoice } from '../../models/payment-details.model';
import { LoadingService } from '../../services/loading.service';

@Component({
  selector: 'app-payment-details',
  templateUrl: './payment-details.component.html',
  styleUrl: './payment-details.component.css'
})
export class PaymentDetailsComponent implements OnInit, OnDestroy {
  public paymentId: string = '';
  public paymentDetails: PaymentDetails | null = null;
  public invoice: Invoice | null = null;
  public isLoading: boolean = true;
  public isLoadingInvoice: boolean = false;
  public error: string | null = null;
  public invoiceError: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private paymentDetailsService: PaymentDetailsService,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.paymentId = params['id'];
      if (this.paymentId) {
        this.loadPaymentDetails();
      } else {
        this.error = 'شناسه پرداخت معتبر نیست';
        this.isLoading = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public loadPaymentDetails(): void {
    this.isLoading = true;
    this.error = null;
    this.loadingService.next(true);

    this.paymentDetailsService.getPaymentDetails(this.paymentId).pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.isLoading = false;
        this.loadingService.next(false);
      })
    ).subscribe({
      next: (response) => {
        if (response.error) {
          this.error = response.message || 'خطا در دریافت اطلاعات پرداخت';
        } else {
          this.paymentDetails = response.result;
          this.loadInvoice();
        }
      },
      error: (error) => {
        this.error = 'خطا در ارتباط با سرور. لطفاً مجدداً تلاش کنید.';
        console.error('Error loading payment details:', error);
      }
    });
  }

  private loadInvoice(): void {
    if (!this.paymentId) return;

    this.isLoadingInvoice = true;
    this.invoiceError = null;

    this.paymentDetailsService.getInvoice(this.paymentId).pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.isLoadingInvoice = false;
      })
    ).subscribe({
      next: (response) => {
        if (response.error) {
          this.invoiceError = response.message || 'خطا در دریافت فاکتور';
        } else {
          this.invoice = response.result;
        }
      },
      error: (error) => {
        this.invoiceError = 'خطا در دریافت فاکتور';
        console.error('Error loading invoice:', error);
      }
    });
  }

  public printInvoice(): void {
    if (typeof window !== 'undefined') {
      // Add print-specific class to body
      document.body.classList.add('printing');

      // Trigger print
      window.print();

      // Remove print class after printing
      setTimeout(() => {
        document.body.classList.remove('printing');
      }, 1000);
    }
  }

  public downloadInvoice(): void {
    if (!this.paymentId) return;

    this.loadingService.next(true);

    this.paymentDetailsService.downloadInvoicePDF(this.paymentId).pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.loadingService.next(false);
      })
    ).subscribe({
      next: (blob) => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `invoice-${this.paymentId}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading invoice:', error);
        // You could show a toast notification here
      }
    });
  }

  public goBack(): void {
    // Try to go back to history page, fallback to dashboard
    if (window.history.length > 1) {
      window.history.back();
    } else {
      this.router.navigate(['/history']);
    }
  }

  public retryLoadPayment(): void {
    this.loadPaymentDetails();
  }

  public retryLoadInvoice(): void {
    this.loadInvoice();
  }

  // Helper method to format currency
  public formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fa-IR').format(amount);
  }

  // Helper method to check if payment is successful
  public isPaymentSuccessful(): boolean {
    return this.paymentDetails?.status === 'SUCCESS';
  }

  // Helper method to get status color class
  public getStatusColorClass(): string {
    if (!this.paymentDetails) return '';

    switch (this.paymentDetails.status) {
      case 'SUCCESS':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'CANCELED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200';
    }
  }
}
