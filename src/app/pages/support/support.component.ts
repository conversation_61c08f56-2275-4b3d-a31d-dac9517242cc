import { Component } from '@angular/core';
import { SUPPORT_TABS, SelectedSupportTab } from '../../constants/tab-definitions';
import { TabItem } from '../../components/generic-tabbar/generic-tabbar.component';
import { CreateTicketRequest, SupportTicket } from '../../models/support-ticket.model';

@Component({
  selector: 'app-support',
  templateUrl: './support.component.html',
  styleUrl: './support.component.css'
})
export class SupportComponent {
  public selectedTab: SelectedSupportTab = 'CREATE_TICKET';
  public tabs: TabItem[] = SUPPORT_TABS;

  // Mock tickets for demonstration
  public mockTickets: SupportTicket[] = [
    {
      id: '12345',
      title: 'مشکل در پرداخت',
      description: 'پرداخت من انجام شده اما اعتبار به حساب اضافه نشده است',
      category: 'PAYMENT',
      priority: 'HIGH',
      status: 'IN_PROGRESS',
      createdAt: new Date(Date.now() - 86400000), // 1 day ago
      updatedAt: new Date(),
      userId: 'user123'
    },
    {
      id: '12346',
      title: 'سوال درباره تخفیف',
      description: 'چگونه می‌توانم از تخفیف‌های ویژه استفاده کنم؟',
      category: 'GENERAL',
      priority: 'MEDIUM',
      status: 'RESOLVED',
      createdAt: new Date(Date.now() - 172800000), // 2 days ago
      updatedAt: new Date(Date.now() - 86400000),
      userId: 'user123'
    },
    {
      id: '12347',
      title: 'خطا در ورود به حساب کاربری',
      description: 'نمی‌توانم وارد حساب کاربری خود شوم',
      category: 'TECHNICAL',
      priority: 'URGENT',
      status: 'OPEN',
      createdAt: new Date(Date.now() - 3600000), // 1 hour ago
      updatedAt: new Date(Date.now() - 3600000),
      userId: 'user123'
    }
  ];

  public onTabChange(tab: string): void {
    this.selectedTab = tab as SelectedSupportTab;
  }

  public onTicketSubmit(ticket: CreateTicketRequest): void {
    // Here you would typically send the ticket to your backend service
    console.log('New ticket submitted:', ticket);

    // For demo purposes, add it to mock tickets
    const newTicket: SupportTicket = {
      id: Math.random().toString(36).substr(2, 9),
      ...ticket,
      status: 'OPEN',
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'user123'
    };

    this.mockTickets.unshift(newTicket);
    this.selectedTab = 'MY_TICKETS';
  }
}
