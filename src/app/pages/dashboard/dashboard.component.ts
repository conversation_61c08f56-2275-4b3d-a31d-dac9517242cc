import { Component } from '@angular/core';
import { CardData } from '../../components/cards/generic-card/generic-card.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {
  public goldCardData: CardData = {
    title: 'کارت اعتباری سلام کارت طلایی',
    balance: '36.789.000',
    currency: 'ریال',
    cardNumber: '2566-9922-7896-1234',
    cvv: '1234',
    expiry: '08/15'
  };

  public debitCardData: CardData = {
    title: 'کارت اعتباری خرید اقساطی',
    balance: '36.789.000',
    currency: 'ریال',
    cardNumber: '2566-9922-7896-1234',
    cvv: '1234',
    expiry: '08/15'
  };
}
