import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { TicketThread, TicketMessage, CreateMessageRequest, SupportTicket } from '../../models/support-ticket.model';

@Component({
  selector: 'app-ticket-thread',
  templateUrl: './ticket-thread.component.html',
  styleUrl: './ticket-thread.component.css'
})
export class TicketThreadComponent implements OnInit {
  public ticketThread?: TicketThread;
  public isLoading: boolean = true;
  public newMessage: string = '';
  public isSending: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    const ticketId = this.route.snapshot.paramMap.get('id');
    if (ticketId) {
      this.loadTicketThread(ticketId);
    } else {
      this.router.navigate(['/support']);
    }
  }

  private loadTicketThread(ticketId: string): void {
    // Mock data - in real app, this would be a service call
    setTimeout(() => {
      this.ticketThread = this.getMockTicketThread(ticketId);
      this.isLoading = false;
    }, 500);
  }

  private getMockTicketThread(ticketId: string): TicketThread {
    const mockTicket: SupportTicket = {
      id: ticketId,
      title: 'مشکل در پرداخت',
      description: 'پرداخت من انجام شده اما اعتبار به حساب اضافه نشده است',
      category: 'PAYMENT',
      priority: 'HIGH',
      status: 'IN_PROGRESS',
      createdAt: new Date(Date.now() - 86400000),
      updatedAt: new Date(),
      userId: 'user123'
    };

    const mockMessages: TicketMessage[] = [
      {
        id: '1',
        ticketId: ticketId,
        content: 'پرداخت من انجام شده اما اعتبار به حساب اضافه نشده است. شماره تراکنش: 1234567890',
        createdAt: new Date(Date.now() - 86400000),
        messageType: 'USER_MESSAGE',
        authorName: 'علی احمدی',
        authorRole: 'USER',
        isRead: true
      },
      {
        id: '2',
        ticketId: ticketId,
        content: 'وضعیت تیکت به "در حال بررسی" تغییر یافت',
        createdAt: new Date(Date.now() - 82800000),
        messageType: 'STATUS_CHANGE',
        authorRole: 'SYSTEM',
        isRead: true
      },
      {
        id: '3',
        ticketId: ticketId,
        content: 'سلام و وقت بخیر. تیکت شما دریافت شد و در حال بررسی است. لطفاً شماره تراکنش و تاریخ پرداخت را ارسال کنید.',
        createdAt: new Date(Date.now() - 79200000),
        messageType: 'SUPPORT_RESPONSE',
        authorName: 'محمد رضایی',
        authorRole: 'SUPPORT',
        isRead: true
      },
      {
        id: '4',
        ticketId: ticketId,
        content: 'شماره تراکنش: 1234567890\nتاریخ پرداخت: 1403/05/15\nمبلغ: 50,000 تومان',
        createdAt: new Date(Date.now() - 75600000),
        messageType: 'USER_MESSAGE',
        authorName: 'علی احمدی',
        authorRole: 'USER',
        isRead: true
      },
      {
        id: '5',
        ticketId: ticketId,
        content: 'متشکرم. اطلاعات شما بررسی شد و اعتبار به حساب شما اضافه گردید. لطفاً بررسی کنید.',
        createdAt: new Date(Date.now() - 3600000),
        messageType: 'SUPPORT_RESPONSE',
        authorName: 'محمد رضایی',
        authorRole: 'SUPPORT',
        isRead: false
      }
    ];

    return {
      ticket: mockTicket,
      messages: mockMessages,
      canRespond: mockTicket.status !== 'CLOSED',
      lastActivity: new Date()
    };
  }

  public sendMessage(): void {
    if (!this.newMessage.trim() || !this.ticketThread || this.isSending) {
      return;
    }

    this.isSending = true;

    const messageRequest: CreateMessageRequest = {
      ticketId: this.ticketThread.ticket.id,
      content: this.newMessage.trim()
    };

    // Mock sending - in real app, this would be a service call
    setTimeout(() => {
      const newMessage: TicketMessage = {
        id: Date.now().toString(),
        ticketId: this.ticketThread!.ticket.id,
        content: messageRequest.content,
        createdAt: new Date(),
        messageType: 'USER_MESSAGE',
        authorName: 'علی احمدی',
        authorRole: 'USER',
        isRead: true
      };

      this.ticketThread!.messages.push(newMessage);
      this.newMessage = '';
      this.isSending = false;
      
      // Scroll to bottom after message is added
      setTimeout(() => this.scrollToBottom(), 100);
    }, 1000);
  }

  public goBack(): void {
    this.router.navigate(['/support']);
  }

  public getStatusLabel(status: string): string {
    const statusMap: { [key: string]: string } = {
      'OPEN': 'باز',
      'IN_PROGRESS': 'در حال بررسی',
      'RESOLVED': 'حل شده',
      'CLOSED': 'بسته شده'
    };
    return statusMap[status] || status;
  }

  public getStatusColor(status: string): string {
    const colorMap: { [key: string]: string } = {
      'OPEN': 'text-blue-600 dark:text-blue-400',
      'IN_PROGRESS': 'text-amber-600 dark:text-amber-400',
      'RESOLVED': 'text-green-600 dark:text-green-400',
      'CLOSED': 'text-slate-600 dark:text-slate-400'
    };
    return colorMap[status] || '';
  }

  public getPriorityLabel(priority: string): string {
    const priorityMap: { [key: string]: string } = {
      'LOW': 'کم',
      'MEDIUM': 'متوسط',
      'HIGH': 'بالا',
      'URGENT': 'فوری'
    };
    return priorityMap[priority] || priority;
  }

  public getPriorityColor(priority: string): string {
    const colorMap: { [key: string]: string } = {
      'LOW': 'text-slate-600 dark:text-slate-400',
      'MEDIUM': 'text-amber-600 dark:text-amber-400',
      'HIGH': 'text-orange-600 dark:text-orange-400',
      'URGENT': 'text-red-600 dark:text-red-400'
    };
    return colorMap[priority] || '';
  }

  private scrollToBottom(): void {
    const messagesContainer = document.querySelector('.chat-messages');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }

  // New methods for modern chat UI
  public onEnterPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Allow new line with Shift+Enter
        return;
      }
      event.preventDefault();
      if (this.newMessage.trim() && !this.isSending) {
        this.sendMessage();
      }
    }
  }

  public getStatusBadgeClass(status: string): string {
    const baseClasses = 'px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2 text-sm font-medium';
    switch (status) {
      case 'RESOLVED':
        return `${baseClasses} bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300`;
      case 'IN_PROGRESS':
        return `${baseClasses} bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300`;
      case 'OPEN':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300`;
      case 'CLOSED':
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300`;
      default:
        return `${baseClasses} bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300`;
    }
  }

  public getStatusIcon(status: string): SafeHtml {
    let iconSvg = '';
    switch (status) {
      case 'RESOLVED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        break;
      case 'IN_PROGRESS':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>';
        break;
      case 'OPEN':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
        break;
      case 'CLOSED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414L9 6.586 7.707 5.293a1 1 0 00-1.414 1.414L7.586 8l-1.293 1.293a1 1 0 101.414 1.414L9 9.414l1.293 1.293a1 1 0 001.414-1.414L10.414 8l1.293-1.293z" clip-rule="evenodd"></path></svg>';
        break;
      default:
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
    }
    return this.sanitizer.bypassSecurityTrustHtml(iconSvg);
  }

  public getPriorityBadgeClass(priority: string): string {
    const baseClasses = 'px-2 py-1 rounded-md flex flex-row-reverse items-center space-x-reverse space-x-1 text-xs font-medium';
    switch (priority) {
      case 'URGENT':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300`;
      case 'HIGH':
        return `${baseClasses} bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300`;
      case 'MEDIUM':
        return `${baseClasses} bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300`;
      case 'LOW':
        return `${baseClasses} bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300`;
      default:
        return `${baseClasses} bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300`;
    }
  }

  public getPriorityDotClass(priority: string): string {
    const baseClasses = 'w-2 h-2 rounded-full';
    switch (priority) {
      case 'URGENT':
        return `${baseClasses} bg-red-400 animate-pulse`;
      case 'HIGH':
        return `${baseClasses} bg-orange-400`;
      case 'MEDIUM':
        return `${baseClasses} bg-amber-400`;
      case 'LOW':
        return `${baseClasses} bg-slate-400`;
      default:
        return `${baseClasses} bg-slate-400`;
    }
  }
}
