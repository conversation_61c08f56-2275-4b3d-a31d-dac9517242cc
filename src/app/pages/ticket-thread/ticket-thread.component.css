/* Modern Chat-like Ticket Thread Styles */

.chat-container {
    @apply flex flex-col h-screen bg-slate-50 dark:bg-slate-900 py-12;
}

/* Chat Header */
.chat-header {
    @apply fixed inset-x-0 left-0 right-0 top-[58px] z-10;
    @apply bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 shadow-sm;
}

.header-content {
    @apply flex items-center flex-row-reverse space-x-reverse p-4 space-x-4 max-w-4xl mx-auto;
}

.back-btn {
    @apply flex items-center justify-center w-10 h-10 rounded-full bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200;
}

.back-btn:hover {
    transform: translateX(2px);
}

.ticket-header-info {
    @apply flex-grow flex flex-col items-stretch px-3;
}

.ticket-title-section {
    @apply text-right mr-4 w-full;
}

.ticket-title {
    @apply text-lg font-bold text-slate-800 dark:text-slate-100 mb-1 leading-tight;
    font-family: 'yekan-b';
}

.ticket-meta-row {
    @apply flex flex-row-reverse items-center space-x-reverse space-x-3 text-sm text-slate-500 dark:text-slate-400;
}

.ticket-id {
    @apply font-mono bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded text-xs;
}

.ticket-badges {
    @apply flex flex-col space-y-2;
}

/* Chat Messages Area */


.messages-wrapper {
    @apply max-w-4xl mx-auto p-4 space-y-6 pt-[183px];
    min-height: 100%;
}

/* Modern Chat Input */
.chat-input-container {
    @apply bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-4;
    backdrop-filter: blur(10px);
}

.input-wrapper {
    @apply max-w-4xl mx-auto;
}

.typing-indicator {
    @apply flex items-center space-x-2 mb-3 text-sm text-slate-500 dark:text-slate-400;
}

.typing-dots {
    @apply flex space-x-1;
}

.typing-dots span {
    @apply w-2 h-2 bg-slate-400 rounded-full animate-bounce;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.1s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.2s;
}

.message-input-area {
    @apply mb-16;
}

.input-container {
    @apply flex items-end space-x-3 bg-slate-50 dark:bg-slate-700/50 rounded-2xl p-3 border border-slate-200 dark:border-slate-600;
    transition: all 0.2s ease-in-out;
}

.input-container:focus-within {
    @apply border-amber-500 shadow-lg;
    transform: translateY(-1px);
}

.message-input {
    @apply flex-1 bg-transparent border-none outline-none resize-none text-slate-800 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400 text-right;
    max-height: 120px;
    min-height: 24px;
    line-height: 1.5;
}

.input-actions {
    @apply flex items-center space-x-2;
}

.action-btn {
    @apply flex items-center justify-center w-8 h-8 rounded-full text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200;
}

.send-btn {
    @apply flex items-center justify-center w-10 h-10 rounded-full bg-amber-500 hover:bg-amber-600 disabled:bg-slate-300 disabled:cursor-not-allowed text-white transition-all duration-200;
}

.send-btn:not(:disabled):hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.send-btn.sending {
    @apply animate-pulse;
}

.send-spinner {
    @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Closed Notice */
.chat-closed-notice {
    @apply bg-slate-100 dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-6;
}

.closed-content {
    @apply max-w-md mx-auto text-center space-y-4;
}

.closed-icon {
    @apply text-slate-400 dark:text-slate-500;
}

.closed-text h4 {
    @apply text-lg font-semibold text-slate-700 dark:text-slate-200 mb-2;
}

.closed-text p {
    @apply text-sm text-slate-600 dark:text-slate-400;
}

/* Loading State */
.loading-overlay {
    @apply flex items-center justify-center h-screen bg-slate-50 dark:bg-slate-900;
}

.loading-content {
    @apply text-center space-y-4;
}

.loading-spinner {
    @apply w-10 h-10 border-4 border-amber-200 border-t-amber-500 rounded-full animate-spin mx-auto;
}

.loading-text {
    @apply text-slate-600 dark:text-slate-400 font-medium;
}

/* Error State */
.chat-error-state {
    @apply flex items-center justify-center h-screen bg-slate-50 dark:bg-slate-900;
}

.error-content {
    @apply text-center space-y-6 max-w-md mx-auto p-6;
}

.error-icon {
    @apply text-red-400 dark:text-red-500;
}

.error-text h3 {
    @apply text-xl font-semibold text-slate-800 dark:text-slate-100 mb-2;
}

.error-text p {
    @apply text-slate-600 dark:text-slate-400;
}

.error-retry-btn {
    @apply flex items-center justify-center space-x-2 px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-lg transition-colors font-medium mx-auto;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-container {
    animation: fadeIn 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        @apply px-3 space-x-3;
    }

    .ticket-title {
        @apply text-lg;
    }

    .ticket-badges {
        @apply flex-row space-y-0 space-x-2;
    }

    .messages-wrapper {
        @apply px-3;
    }

    .input-wrapper {
        @apply px-3;
    }
}

@media (max-width: 640px) {
    .ticket-header-info {
        @apply flex-col items-start space-y-3;
    }

    .ticket-badges {
        @apply flex-row flex-wrap gap-2;
    }

    .input-container {
        @apply p-2;
    }

    .send-btn {
        @apply w-9 h-9;
    }
}
