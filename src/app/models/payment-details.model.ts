export type PaymentStatus = 'SUCCESS' | 'FAILED' | 'PENDING' | 'CANCELED';

export interface PaymentDetails {
  paymentId: string;
  amount: string;
  currency: string;
  status: PaymentStatus;
  statusText: string;
  timestamp: Date;
  paymentMethod: string;
  referenceNumber: string;
  cardNumber: string;
  description?: string;
  merchantName?: string;
  terminalId?: string;
}

export interface InvoiceItem {
  id: string;
  description: string; // شرح - can be multi-line
  quantity: number; // تعداد
  unitPrice: number; // قیمت واحد
  totalPrice: number; // قیمت کل
  discountPercent: number; // درصد تخفیف
  discountAmount: number; // مبلغ تخفیف
  taxAmount: number; // ارزش افزوده
  finalAmount: number; // مبلغ نهایی
}

export interface InvoiceSummary {
  totalDiscount: number; // کل مبلغ تخفیف دریافتی
  totalTax: number; // کل مبلغ ارزش افزوده پرداختی
  totalAmount: number; // کل مبلغ پرداختی
  subtotal?: number; // مجموع قبل از تخفیف و مالیات
  itemCount?: number; // تعداد اقلام
}

export interface Invoice {
  invoiceNumber: string; // شماره صورتحساب
  shopName: string; // نام فروشگاه
  shopLogo?: string; // لوگو فروشگاه
  paymentId: string; // شناسه پرداخت
  paymentDateTime: Date; // تاریخ و زمان پرداخت
  cashRegisterNumber: string; // شماره صندوق
  items: InvoiceItem[]; // اقلام فاکتور
  summary: InvoiceSummary; // خلاصه فاکتور
  dailyMessage?: string; // پیام روزانه
  shopAddress?: string; // آدرس فروشگاه
  shopPhone?: string; // تلفن فروشگاه
  shopTaxId?: string; // شناسه مالیاتی فروشگاه
  customerInfo?: CustomerInfo; // اطلاعات مشتری
  notes?: string; // یادداشت‌ها
}

export interface CustomerInfo {
  name?: string;
  phone?: string;
  email?: string;
  address?: string;
  customerId?: string;
}

// Sample data for development/testing
export const SamplePaymentDetails: PaymentDetails = {
  paymentId: 'PAY_*********0',
  amount: '2.450.000',
  currency: 'ریال',
  status: 'SUCCESS',
  statusText: 'موفق',
  timestamp: new Date(),
  paymentMethod: 'کارت بانکی',
  referenceNumber: 'REF_987654321',
  cardNumber: '**** **** **** 1234',
  description: 'خرید از فروشگاه آنلاین',
  merchantName: 'فروشگاه نمونه',
  terminalId: 'T001'
};

export const SampleInvoice: Invoice = {
  invoiceNumber: 'INV_2024_001234',
  shopName: 'فروشگاه نمونه تجاری',
  shopLogo: '/assets/images/shop-logo.png',
  paymentId: 'PAY_*********0',
  paymentDateTime: new Date(),
  cashRegisterNumber: 'REG_001',
  shopAddress: 'تهران، خیابان ولیعصر، پلاک ۱۲۳',
  shopPhone: '021-12345678',
  shopTaxId: '*********',
  items: [
    {
      id: '1',
      description: 'محصول نمونه شماره یک\nبا توضیحات کامل\nو جزئیات بیشتر',
      quantity: 2,
      unitPrice: 500000,
      totalPrice: 1000000,
      discountPercent: 10,
      discountAmount: 100000,
      taxAmount: 81000,
      finalAmount: 981000
    },
    {
      id: '2',
      description: 'محصول نمونه شماره دو\nبا کیفیت عالی',
      quantity: 1,
      unitPrice: 750000,
      totalPrice: 750000,
      discountPercent: 5,
      discountAmount: 37500,
      taxAmount: 64125,
      finalAmount: 776625
    },
    {
      id: '3',
      description: 'خدمات ارسال و بسته‌بندی\nارسال سریع',
      quantity: 1,
      unitPrice: 150000,
      totalPrice: 150000,
      discountPercent: 0,
      discountAmount: 0,
      taxAmount: 13500,
      finalAmount: 163500
    }
  ],
  summary: {
    totalDiscount: 137500,
    totalTax: 158625,
    totalAmount: 1921125,
    subtotal: 1900000,
    itemCount: 4
  },
  dailyMessage: 'از خرید شما متشکریم!\nلطفاً برای دریافت خدمات پس از فروش با شماره پشتیبانی تماس بگیرید.\nساعات کاری: شنبه تا پنج‌شنبه ۸ تا ۲۰',
  customerInfo: {
    name: 'مشتری نمونه',
    phone: '09*********',
    customerId: 'CUST_001'
  },
  notes: 'فاکتور قابل استرداد تا ۷ روز پس از خرید'
};
