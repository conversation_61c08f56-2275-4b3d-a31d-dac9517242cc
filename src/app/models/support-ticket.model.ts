export type TicketStatus = 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
export type TicketPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
export type TicketCategory = 'TECHNICAL' | 'PAYMENT' | 'ACCOUNT' | 'GENERAL';

export interface SupportTicket {
  id: string;
  title: string;
  description: string;
  category: TicketCategory;
  priority: TicketPriority;
  status: TicketStatus;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  assignedTo?: string;
  responses?: TicketResponse[];
}

export interface TicketResponse {
  id: string;
  ticketId: string;
  message: string;
  isFromSupport: boolean;
  createdAt: Date;
  authorName: string;
}

export interface CreateTicketRequest {
  title: string;
  description: string;
  category: TicketCategory;
  priority: TicketPriority;
}

export interface TicketMessage {
  id: string;
  ticketId: string;
  content: string;
  createdAt: Date;
  messageType: MessageType;
  authorName?: string;
  authorRole?: 'USER' | 'SUPPORT' | 'SYSTEM';
  attachments?: MessageAttachment[];
  isRead?: boolean;
}

export interface MessageAttachment {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  downloadUrl: string;
}

export interface TicketThread {
  ticket: SupportTicket;
  messages: TicketMessage[];
  canRespond: boolean;
  lastActivity: Date;
}

export interface CreateMessageRequest {
  ticketId: string;
  content: string;
  attachments?: File[];
}

export type MessageType = 'USER_MESSAGE' | 'SUPPORT_RESPONSE' | 'STATUS_CHANGE' | 'SYSTEM_NOTIFICATION';
