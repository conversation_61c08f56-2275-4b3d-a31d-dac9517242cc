import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ResponseDTO } from '../models/response.dto';
import { environment } from '../../environments/environment.development';
import { UserDTO } from '../models/user.dto';


@Injectable({
    providedIn: 'root'
})
export class UserApi {

    constructor(private http: HttpClient) { }

    public ContextUser(): Observable<ResponseDTO<UserDTO>> {
        return this.http.get<ResponseDTO<UserDTO>>(`${environment.ApiBaseURL}/user`);
    }
}
