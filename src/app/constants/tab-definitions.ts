import { TabItem } from '../components/generic-tabbar/generic-tabbar.component';

// Support Page Tabs
export const SUPPORT_TABS: TabItem[] = [
  {
    id: 'MY_TICKETS',
    label: 'تیکت های من',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
             <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
           </svg>`
  },
  {
    id: 'CREATE_TICKET',
    label: 'ایجاد تیکت',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
             <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
           </svg>`
  }
];

// History Page Tabs
export const HISTORY_TABS: TabItem[] = [
  {
    id: 'TRANSACTIONS',
    label: 'تراکنش ها',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 22" stroke-width="1.5" stroke="currentColor" class="size-6">
             <path stroke-linecap="round" stroke-linejoin="round" d="M4.257,7.002 L12.743,7.002 M4.257,12.002 L11.257,12.002 M16,3.508 L16,20.502 L12.25,19.002 L8.5,20.502 L4.75,19.002 L1,20.502 L1,3.508 C1,2.401 1.806,1.452 2.907,1.324 C6.623,0.892 10.377,0.892 14.093,1.324 C15.193,1.452 16,2.401 16,3.508 Z" />
           </svg>`
  },
  {
    id: 'ORDERS',
    label: 'شارژ نقدی',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
             <path stroke-linecap="round" stroke-linejoin="round" d="m9 14.25 6-6m4.5-3.493V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185ZM9.75 9h.008v.008H9.75V9Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm4.125 4.5h.008v.008h-.008V13.5Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
           </svg>`
  }
];

// About Page Tabs
export const ABOUT_TABS: TabItem[] = [
  {
    id: 'TERMS_CONDITIONS',
    label: 'قوانین و مقررات',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971Z" />
          </svg>`
  },
  {
    id: 'ABOUT_US',
    label: 'درباره ما',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
             <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
           </svg>`
  }
];

// Type definitions for backward compatibility
export type SelectedSupportTab = 'CREATE_TICKET' | 'MY_TICKETS';
export type SelectedHistoryTab = 'ORDERS' | 'TRANSACTIONS';
export type SelectedAboutTab = 'ABOUT_US' | 'TERMS_CONDITIONS';
