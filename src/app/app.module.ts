import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { DatePipe, CommonModule } from '@angular/common';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LayoutComponent } from './components/layout/layout.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { ProfileComponent } from './pages/profile/profile.component';
import { OtpVerifyComponent } from './pages/otp-verify/otp-verify.component';
import { ThemeToggleComponent } from './components/theme-toggle/theme-toggle.component';
import { AnonymousLayoutComponent } from './components/anonymous-layout/anonymous-layout.component';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { AuthComponent } from './pages/auth/auth.component';
import { CodeInputModule } from 'angular-code-input';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { AuthInterceptor } from './web/auth.interceptor';
import { ResponseInterceptor } from './web/response.interceptor';

import { register } from 'swiper/element/bundle';
// Removed old card components - replaced with GenericCardComponent
import { OrderFormComponent } from './components/order-form/order-form.component';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FormsModule } from '@angular/forms';
import { HistoryComponent } from './pages/history/history.component';
import { SupportComponent } from './pages/support/support.component';
import { AboutComponent } from './pages/about/about.component';
import { PersonIdentificationComponent } from './components/person-identification/person-identification.component';
import { ProfileFormComponent } from './components/profile-form/profile-form.component';
// Removed old history row components - replaced with GenericHistoryRowComponent
import { TicketFormComponent } from './components/ticket-form/ticket-form.component';
import { TicketRowComponent } from './components/ticket-row/ticket-row.component';
import { SelectionModalComponent } from './components/selection-modal/selection-modal.component';
import { TicketThreadComponent } from './pages/ticket-thread/ticket-thread.component';
import { TicketMessageComponent } from './components/ticket-message/ticket-message.component';
import { GenericTabbarComponent } from './components/generic-tabbar/generic-tabbar.component';
import { GenericCardComponent } from './components/cards/generic-card/generic-card.component';
import { GenericHistoryRowComponent } from './components/history-row/generic-history-row.component';
import { LogoComponent } from './components/shared/logo/logo.component';
import { BasePageComponent } from './components/shared/base-page/base-page.component';
import { DrawerComponent } from './components/drawer/drawer.component';
import { DrawerMenuComponent } from './components/drawer-menu/drawer-menu.component';
import { HistoryDetailsComponent } from './pages/history-details/history-details.component';
import { PaymentDetailsComponent } from './pages/payment-details/payment-details.component';

register()

@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent,
    DashboardComponent,
    ProfileComponent,
    OtpVerifyComponent,
    AnonymousLayoutComponent,
    ThemeToggleComponent,
    AuthComponent,
    // GoldCardComponent, DebitCardComponent - replaced with GenericCardComponent
    OrderFormComponent,
    HistoryComponent,
    SupportComponent,
    AboutComponent,
    PersonIdentificationComponent,
    ProfileFormComponent,
    // OrderHistoryRowComponent, PaymentHistoryRowComponent - replaced with GenericHistoryRowComponent
    TicketFormComponent,
    TicketRowComponent,
    SelectionModalComponent,
    TicketThreadComponent,
    TicketMessageComponent,
    GenericTabbarComponent,
    GenericCardComponent,
    GenericHistoryRowComponent,
    LogoComponent,
    BasePageComponent,
    DrawerComponent,
    DrawerMenuComponent,
    HistoryDetailsComponent,
    PaymentDetailsComponent,
  ],
  imports: [
    BrowserModule,
    CommonModule,
    AppRoutingModule,
    NgxMaskDirective,
    NgxMaskPipe,
    CodeInputModule,
    NgApexchartsModule,
    FormsModule
  ],
  providers: [
    DatePipe,
    provideNgxMask(),
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ResponseInterceptor,
      multi: true
    },
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule { }
