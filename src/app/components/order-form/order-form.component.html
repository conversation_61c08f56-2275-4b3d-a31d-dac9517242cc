<div class="card-row">
    <div
        class="flex flex-row-reverse space-x-reverse space-x-4 items-center justify-between relative drop-shadow-lg font-bold pb-1">
        <h6 class="title">خرید شارژ نقدی اعتبار</h6>
        <div class="flex items-baseline justify-between absolute left-0 top-2 text-amber-500">
            <span class="text-lime-500 p-0 text-6xl pr-1">+</span>
            <h1 class="text-6xl font-sans flex space-x-0 items-baseline">
                <span class="p-0">{{discount}}</span>
                <h1 class="text-3xl">%</h1>
            </h1>
        </div>
    </div>
    <div class="input-group">
        <label class="label-text rtl">مبلغ خرید (تومان)</label>
        <input type="tel" class="input-text font-bold text-lg number" mask="separator.0"
            (input)="cashAmountChange($event)" [dropSpecialCharacters]="true" thousandSeparator="," [leadZero]="true"
            [(ngModel)]="cash" />
    </div>

    <div class="input-group">
        <label class="label-text rtl">اعتبار دریافتی</label>
        <input type="tel" class="input-text font-bold !text-xl no-focus number text-lime-600 dark:text-lime-500"
            mask="separator.0" readonly [dropSpecialCharacters]="true" thousandSeparator="," [leadZero]="true"
            [(ngModel)]="credit" />
    </div>

    <button class="button green iconic mt-6 mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            class="size-7">
            <path stroke-linecap="round" stroke-linejoin="round"
                d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
        </svg>
        <div class="font-bold">تایید و پرداخت خرید</div>
    </button>
</div>