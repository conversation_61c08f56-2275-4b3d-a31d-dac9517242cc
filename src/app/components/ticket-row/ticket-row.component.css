/* Enhanced Ticket Row Styles - Matching History Row Design */

.status-badge {
    transition: all 0.2s ease-in-out;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-icon svg {
    transition: transform 0.2s ease-in-out;
}

.status-badge:hover .status-icon svg {
    transform: scale(1.1);
}

/* Pulse animation for in-progress status */
.status-badge.bg-amber-100 {
    animation: pulse-amber 2s infinite;
}

@keyframes pulse-amber {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Success status glow effect */
.status-badge.bg-emerald-100 {
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

/* Open status subtle glow */
.status-badge.bg-blue-100 {
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Enhanced card hover effect */
.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Priority indicator animations */
.priority-indicator {
    transition: all 0.2s ease-in-out;
}

.priority-indicator:hover {
    transform: scale(1.2);
}

/* Action button enhancements */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .status-badge.bg-emerald-100 {
        box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
    }

    .status-badge.bg-blue-100 {
        box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
    }

    .content-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
}
