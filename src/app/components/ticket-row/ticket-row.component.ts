import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { SupportTicket, TicketStatus, TicketPriority, TicketCategory } from '../../models/support-ticket.model';

@Component({
  selector: 'app-ticket-row',
  templateUrl: './ticket-row.component.html',
  styleUrl: './ticket-row.component.css'
})
export class TicketRowComponent {
  @Input() ticket?: SupportTicket;

  constructor(private router: Router, private sanitizer: DomSanitizer) {}

  // Mock data for demonstration
  public mockTicket: SupportTicket = {
    id: '12345',
    title: 'مشکل در پرداخت',
    description: 'پرداخت من انجام شده اما اعتبار به حساب اضافه نشده است',
    category: 'PAYMENT',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: 'user123'
  };

  get displayTicket(): SupportTicket {
    return this.ticket || this.mockTicket;
  }

  getStatusLabel(status: TicketStatus): string {
    const statusMap = {
      'OPEN': 'باز',
      'IN_PROGRESS': 'در حال بررسی',
      'RESOLVED': 'حل شده',
      'CLOSED': 'بسته شده'
    };
    return statusMap[status];
  }

  getStatusColor(status: TicketStatus): string {
    const colorMap = {
      'OPEN': 'text-blue-600 dark:text-blue-400',
      'IN_PROGRESS': 'text-amber-600 dark:text-amber-400',
      'RESOLVED': 'text-green-600 dark:text-green-400',
      'CLOSED': 'text-slate-600 dark:text-slate-400'
    };
    return colorMap[status];
  }

  getPriorityLabel(priority: TicketPriority): string {
    const priorityMap = {
      'LOW': 'کم',
      'MEDIUM': 'متوسط',
      'HIGH': 'بالا',
      'URGENT': 'فوری'
    };
    return priorityMap[priority];
  }

  getPriorityColor(priority: TicketPriority): string {
    const colorMap = {
      'LOW': 'text-slate-600 dark:text-slate-400',
      'MEDIUM': 'text-amber-600 dark:text-amber-400',
      'HIGH': 'text-orange-600 dark:text-orange-400',
      'URGENT': 'text-red-600 dark:text-red-400'
    };
    return colorMap[priority];
  }

  getCategoryLabel(category: TicketCategory): string {
    const categoryMap = {
      'TECHNICAL': 'فنی',
      'PAYMENT': 'پرداخت',
      'ACCOUNT': 'حساب کاربری',
      'GENERAL': 'عمومی'
    };
    return categoryMap[category];
  }

  getStatusIndicatorColor(status: TicketStatus): string {
    const colorMap = {
      'OPEN': 'bg-blue-500',
      'IN_PROGRESS': 'bg-amber-500',
      'RESOLVED': 'bg-green-500',
      'CLOSED': 'bg-slate-500'
    };
    return colorMap[status];
  }

  public viewTicket(): void {
    const ticketId = this.displayTicket.id;
    this.router.navigate(['/support/ticket', ticketId]);
  }

  get statusBorderClass(): string {
    switch (this.displayTicket.status) {
      case 'RESOLVED':
        return 'border-l-emerald-500';
      case 'IN_PROGRESS':
        return 'border-l-amber-500';
      case 'OPEN':
        return 'border-l-blue-500';
      case 'CLOSED':
        return 'border-l-gray-500';
      default:
        return 'border-l-slate-300';
    }
  }

  get statusBadgeClass(): string {
    switch (this.displayTicket.status) {
      case 'RESOLVED':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'IN_PROGRESS':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'OPEN':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
    }
  }

  get statusIcon(): SafeHtml {
    let iconSvg = '';
    switch (this.displayTicket.status) {
      case 'RESOLVED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        break;
      case 'IN_PROGRESS':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>';
        break;
      case 'OPEN':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
        break;
      case 'CLOSED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414L9 6.586 7.707 5.293a1 1 0 00-1.414 1.414L7.586 8l-1.293 1.293a1 1 0 101.414 1.414L9 9.414l1.293 1.293a1 1 0 001.414-1.414L10.414 8l1.293-1.293z" clip-rule="evenodd"></path></svg>';
        break;
      default:
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
    }
    return this.sanitizer.bypassSecurityTrustHtml(iconSvg);
  }

  getPriorityIndicatorClass(priority: TicketPriority): string {
    const colorMap = {
      'LOW': 'w-2 h-2 rounded-full bg-slate-400',
      'MEDIUM': 'w-2 h-2 rounded-full bg-amber-400',
      'HIGH': 'w-2 h-2 rounded-full bg-orange-400',
      'URGENT': 'w-2 h-2 rounded-full bg-red-400 animate-pulse'
    };
    return colorMap[priority];
  }
}
