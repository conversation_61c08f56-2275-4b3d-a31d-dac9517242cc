<div class="content-card p-5 space-y-4 hover:shadow-lg transition-all duration-300 border-l-4" [ngClass]="statusBorderClass">
    <!-- Header with Status Badge -->
    <div class="flex flex-row-reverse items-center justify-between mb-4">
        <div class="flex flex-row-reverse items-center space-x-reverse space-x-3">
            <div class="status-badge" [ngClass]="statusBadgeClass">
                <div class="status-icon" [innerHTML]="statusIcon"></div>
                <span class="text-sm font-medium">{{ getStatusLabel(displayTicket.status) }}</span>
            </div>
        </div>
        <div class="text-left">
            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">شماره تیکت</div>
            <div class="text-sm font-mono text-slate-700 dark:text-slate-300">#{{ displayTicket.id }}</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="space-y-4">
        <!-- Title and Time Section -->
        <div class="flex flex-row-reverse items-start justify-between">
            <div class="text-right flex-1 ml-4">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">عنوان تیکت</div>
                <h3 class="text-lg font-bold text-slate-800 dark:text-slate-100 leading-relaxed">
                    {{ displayTicket.title }}
                </h3>
            </div>
            <div class="text-left">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">زمان ایجاد</div>
                <div class="text-sm font-mono text-slate-600 dark:text-slate-300">
                    {{ displayTicket.createdAt | date:"yyyy/MM/dd, HH:mm" }}
                </div>
            </div>
        </div>

        <!-- Category and Priority Section -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-right">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">دسته‌بندی</div>
                <div class="text-sm font-medium text-slate-700 dark:text-slate-300">
                    {{ getCategoryLabel(displayTicket.category) }}
                </div>
            </div>
            <div class="text-left">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">اولویت</div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium" [ngClass]="getPriorityColor(displayTicket.priority)">
                        {{ getPriorityLabel(displayTicket.priority) }}
                    </span>
                    <div class="priority-indicator" [ngClass]="getPriorityIndicatorClass(displayTicket.priority)"></div>
                </div>
            </div>
        </div>

        <!-- Action Button -->
        <div class="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
            <button class="w-full flex flex-row-reverse items-center justify-center space-x-reverse space-x-2 px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg transition-colors duration-200 font-medium" (click)="viewTicket()">
                <span>مشاهده جزئیات</span>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
                </svg>
            </button>
        </div>
    </div>
</div>