import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { TabItem } from '../../generic-tabbar/generic-tabbar.component';

@Component({
  selector: 'app-base-page',
  templateUrl: './base-page.component.html',
  styleUrl: './base-page.component.css'
})
export class BasePageComponent implements OnInit {
  @Input() tabs: TabItem[] = [];
  @Input() defaultTab: string = '';
  @Input() showTabbar: boolean = true;
  @Output() onTabChange = new EventEmitter<string>();

  public selectedTab: string = '';

  ngOnInit(): void {
    this.selectedTab = this.defaultTab || (this.tabs.length > 0 ? this.tabs[0].id : '');
  }

  public handleTabChange(tab: string): void {
    this.selectedTab = tab;
    this.onTabChange.emit(tab);
  }
}
