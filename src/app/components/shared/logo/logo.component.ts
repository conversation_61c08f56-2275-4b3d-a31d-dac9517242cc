import { Component, Input } from '@angular/core';

export type LogoSize = 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'app-logo',
  templateUrl: './logo.component.html',
  styleUrl: './logo.component.css'
})
export class LogoComponent {
  @Input() size: LogoSize = 'md';
  @Input() showText: boolean = true;
  @Input() textClass: string = '';

  get containerClass(): string {
    return 'flex flex-row-reverse space-x-reverse space-x-2 items-center';
  }

  get iconClass(): string {
    const sizeClasses = {
      sm: 'size-6',
      md: 'size-8', 
      lg: 'size-10',
      xl: 'size-12'
    };
    return `${sizeClasses[this.size]} text-primary`;
  }

  get textSizeClass(): string {
    const textSizes = {
      sm: 'text-base',
      md: 'text-xl',
      lg: 'text-2xl', 
      xl: 'text-3xl'
    };
    return `font-bold ${textSizes[this.size]} text-slate-700 dark:text-slate-200 ${this.textClass}`;
  }
}
