.drawer {
    @apply fixed inset-0 h-full z-40 pr-14;
    @apply transform transition-transform duration-300 ease-in-out;
    @apply -translate-x-full;
    @apply bg-slate-400/30 dark:bg-slate-900/50;
}

.drawer.open {
    @apply translate-x-0 backdrop-blur-sm;
    @apply shadow-lg shadow-slate-400/15 dark:shadow-black/50;
}

.drawer .container {
    @apply bg-white dark:bg-slate-800;
    @apply h-full mr-14 p-3;
    @apply border-r-2 border-e-slate-300 dark:border-slate-900;
}

.drawer .container .drawer-header {
    @apply flex flex-row-reverse items-center justify-between;
    @apply w-full pb-6;
}

.drawer .container .drawer-header .drawer-button {
    @apply p-2 rounded-full size-12 flex items-stretch justify-stretch;
    @apply focus:bg-slate-100 dark:focus:bg-slate-900;
    @apply text-slate-400 dark:text-slate-300;
    @apply focus:text-amber-500 dark:focus:text-amber-400;
}

.drawer .container .drawer-header .drawer-button svg {
    @apply rounded-full min-w-0;
}