.layout {
    @apply p-0 m-0;
}

.layout .header {
    @apply py-1 bg-slate-50 dark:bg-slate-800 min-h-[58px];
    @apply shadow-md shadow-slate-400/15 dark:shadow-black/50;
    @apply border-b border-b-slate-200 dark:border-b-slate-900;
    @apply fixed inset-x-0 top-0 z-20;
}

.layout .header .nav {
    @apply flex flex-row justify-between items-center space-x-3 px-2;
}

.layout .header .nav .logo {
    @apply flex-grow px-3;
}

.layout .header .nav .drawer-button {
    @apply p-2 rounded-full size-12 flex items-stretch justify-stretch;
    @apply focus:bg-slate-100 dark:focus:bg-slate-900;
    @apply text-slate-400 dark:text-slate-300;
    @apply focus:text-amber-500 dark:focus:text-amber-400;
}

.layout .header .nav .drawer-button svg {
    @apply rounded-full min-w-0;
}

.bottom-nav {
    @apply fixed inset-x-0 bottom-0 z-20 min-h-[58px];
    @apply py-1 px-3 bg-slate-50 dark:bg-slate-800 min-h-[58px];
    @apply border-b border-b-slate-200 dark:border-b-slate-900;
    @apply shadow-[rgba(0,0,0,0.2)_10px_5px_15px_2px];
    @apply dark:shadow-[rgba(0,0,0,0.7)_5px_5px_10px_5px];
    @apply grid grid-cols-5 gap-2;
}

.bottom-nav .nav-item {
    @apply p-1 flex flex-col space-y-1 items-center justify-center;
}

.bottom-nav .nav-item svg {
    @apply size-7;
}

.bottom-nav .nav-item small {
    @apply font-bold text-xs;
}

.bottom-nav .nav-item.selected {
    @apply text-amber-600 dark:text-amber-400;
}
