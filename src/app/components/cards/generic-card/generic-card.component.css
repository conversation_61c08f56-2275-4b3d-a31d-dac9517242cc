.header {
    @apply flex items-center justify-between;
}

.header h1 {
    @apply font-bold text-right flex-grow;
    @apply text-slate-800 drop-shadow;
}

svg {
    @apply size-7 text-slate-300;
}

.body {
    @apply mt-4 flex-grow;
}

.body h6 {
    @apply text-right text-sky-900 font-bold text-sm;
}

.balance {
    @apply flex flex-row-reverse space-x-reverse space-x-2 items-center justify-end mt-2;
}

.balance .value {
    @apply text-2xl font-bold text-slate-800 drop-shadow;
}

.balance .currency {
    @apply text-sm font-bold text-slate-700 drop-shadow;
}

.card-info {
    @apply w-full space-y-1;
}

.card-info .number {
    @apply text-2xl font-bold font-mono text-slate-800 drop-shadow tracking-wider;
    @apply text-center;
}

.card-info .cvv,
.card-info .expire {
    @apply font-medium font-mono text-slate-700 drop-shadow;
}
