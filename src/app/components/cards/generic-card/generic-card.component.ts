import { Component, Input } from '@angular/core';

export interface CardData {
  title: string;
  balance: string;
  currency: string;
  cardNumber: string;
  cvv: string;
  expiry: string;
}

export type CardType = 'gold' | 'debit';

@Component({
  selector: 'app-generic-card',
  templateUrl: './generic-card.component.html',
  styleUrl: './generic-card.component.css'
})
export class GenericCardComponent {
  @Input() cardType: CardType = 'gold';
  @Input() cardData!: CardData;

  get cardClasses(): string {
    return `base-card ${this.cardType}`;
  }

  get gradientClasses(): string {
    switch (this.cardType) {
      case 'gold':
        return 'ring-yellow-500 dark:ring-yellow-600 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-amber-500 via-amber-400 to-amber-600';
      case 'debit':
        return 'ring-slate-300 dark:ring-slate-700 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-slate-500 via-slate-300 to-slate-400';
      default:
        return '';
    }
  }
}
