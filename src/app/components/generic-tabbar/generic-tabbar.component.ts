import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

export interface TabItem {
  id: string;
  label: string;
  icon: string; // SVG path or icon identifier
  safeIcon?: SafeHtml; // Sanitized HTML for display
}

@Component({
  selector: 'app-generic-tabbar',
  templateUrl: './generic-tabbar.component.html',
  styleUrl: './generic-tabbar.component.css'
})
export class GenericTabbarComponent implements OnInit {
  @Input() tabs: TabItem[] = [];
  @Input() defaultTab: string = '';
  @Output() onTabChange = new EventEmitter<string>();

  public selectedTab: string;

  constructor(private sanitizer: DomSanitizer) {
    this.selectedTab = this.defaultTab;
  }

  ngOnInit(): void {
    // Sanitize SVG icons for safe HTML rendering
    this.tabs = this.tabs.map(tab => ({
      ...tab,
      safeIcon: this.sanitizer.bypassSecurityTrustHtml(tab.icon)
    }));

    if (this.tabs.length > 0 && !this.defaultTab) {
      this.selectedTab = this.tabs[0].id;
    } else {
      this.selectedTab = this.defaultTab;
    }
  }

  public setSelectedTab(tabId: string): void {
    this.onTabChange.emit(tabId);
    this.selectedTab = tabId;
  }
}
