/* Modern Chat Message Styles */

.chat-message {
    @apply mb-6;
    animation: slideInMessage 0.3s ease-out;
}

@keyframes slideInMessage {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* System Notifications */
.system-message {
    @apply flex justify-center my-4;
}

.system-notification {
    @apply max-w-md mx-auto;
}

.system-content {
    @apply flex items-center justify-center space-x-3 px-4 py-2 bg-slate-100 dark:bg-slate-800 rounded-full border border-slate-200 dark:border-slate-700;
}

.system-icon {
    @apply text-slate-500 dark:text-slate-400 flex-shrink-0;
}

.system-text {
    @apply text-center;
}

.system-message-text {
    @apply text-sm text-slate-600 dark:text-slate-400 font-medium;
}

.system-time {
    @apply text-xs text-slate-500 dark:text-slate-500 mt-1 block;
}

/* Chat Messages Layout */
.user-message .message-wrapper {
    @apply flex flex-row-reverse items-start space-x-reverse space-x-3 max-w-4xl ml-auto;
}

.support-message .message-wrapper {
    @apply flex items-start space-x-3 max-w-4xl mr-auto;
}

/* Avatar */
.message-avatar {
    @apply flex-shrink-0;
}

.avatar-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center;
}

.user-message .avatar-icon {
    @apply bg-amber-500 text-white;
}

.support-message .avatar-icon {
    @apply bg-blue-500 text-white;
}

/* Message Content */
.message-content-wrapper {
    @apply flex-1 min-w-0;
}

.message-header {
    @apply flex items-center space-x-2 mb-2;
}

.user-message .message-header {
    @apply flex-row-reverse space-x-reverse;
}

.author-name {
    @apply text-sm font-semibold text-slate-700 dark:text-slate-300;
}

.message-timestamp {
    @apply text-xs text-slate-500 dark:text-slate-400;
}

/* Message Bubbles */
.message-bubble {
    @apply relative p-4 rounded-2xl shadow-sm;
    max-width: 70%;
}

.user-message .message-bubble {
    @apply bg-amber-500 text-white rounded-br-md ml-auto;
}

.support-message .message-bubble {
    @apply bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-bl-md;
}

.message-text {
    @apply text-sm leading-relaxed text-right whitespace-pre-wrap break-words;
}

.user-message .message-text {
    @apply text-white;
}

.support-message .message-text {
    @apply text-slate-800 dark:text-slate-200;
}

/* Attachments */
.attachments-container {
    @apply mt-4 pt-3 border-t border-current border-opacity-20;
}

.attachments-header {
    @apply flex items-center space-x-2 mb-3 text-xs font-medium opacity-75;
}

.attachments-count {
    @apply text-current;
}

.attachments-grid {
    @apply space-y-2;
}

.attachment-card {
    @apply flex items-center space-x-3 p-3 rounded-lg bg-black bg-opacity-10 dark:bg-white dark:bg-opacity-10 hover:bg-opacity-20 transition-colors;
}

.attachment-icon {
    @apply flex-shrink-0 text-current opacity-75;
}

.attachment-info {
    @apply flex-1 min-w-0;
}

.attachment-name {
    @apply text-xs font-medium truncate;
}

.attachment-size {
    @apply text-xs opacity-75 mt-1;
}

.attachment-download {
    @apply flex-shrink-0 p-2 rounded-full hover:bg-black hover:bg-opacity-10 dark:hover:bg-white dark:hover:bg-opacity-10 transition-colors;
}

/* Hover Effects */
.message-bubble:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease-in-out;
}

.user-message .message-bubble:hover {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.support-message .message-bubble:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .message-bubble {
        max-width: 85%;
    }

    .message-wrapper {
        @apply space-x-2;
    }

    .avatar-icon {
        @apply w-8 h-8;
    }
}
