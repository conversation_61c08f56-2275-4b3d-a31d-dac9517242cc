import { Component, Input } from '@angular/core';
import { TicketMessage } from '../../models/support-ticket.model';

@Component({
  selector: 'app-ticket-message',
  templateUrl: './ticket-message.component.html',
  styleUrl: './ticket-message.component.css'
})
export class TicketMessageComponent {
  @Input() message!: TicketMessage;

  get isUserMessage(): boolean {
    return this.message.messageType === 'USER_MESSAGE';
  }

  get isSupportResponse(): boolean {
    return this.message.messageType === 'SUPPORT_RESPONSE';
  }

  get isSystemNotification(): boolean {
    return this.message.messageType === 'SYSTEM_NOTIFICATION' || this.message.messageType === 'STATUS_CHANGE';
  }



  get authorDisplayName(): string {
    if (this.message.authorName) {
      return this.message.authorName;
    }
    
    switch (this.message.authorRole) {
      case 'USER':
        return 'شما';
      case 'SUPPORT':
        return 'پشتیبانی';
      case 'SYSTEM':
        return 'سیستم';
      default:
        return 'ناشناس';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 بایت';
    const k = 1024;
    const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
