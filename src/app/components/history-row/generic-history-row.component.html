<div class="content-card p-5 space-y-4 hover:shadow-lg transition-all duration-300 border-l-4"
    [ngClass]="statusBorderClass">
    <!-- Header with Status Badge -->
    <div class="flex flex-row-reverse items-center justify-between mb-4">
        <div class="flex flex-row-reverse items-center space-x-reverse space-x-3">
            <div class="status-badge" [ngClass]="statusBadgeClass">
                <div class="status-icon" [innerHTML]="statusIcon"></div>
                <span class="text-sm font-medium">{{ data.secondaryInfo?.value }}</span>
            </div>
        </div>
        <div class="text-left">
            <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">{{ data.primaryLabel }}</div>
            <div class="text-sm font-mono text-slate-700 dark:text-slate-300">{{ data.primaryId }}</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="space-y-4">
        <!-- Amount and Time Section -->
        <div class="flex flex-row-reverse items-center justify-between">
            <div class="text-right">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">
                    {{ rowType === 'payment' ? 'مبلغ تراکنش' : 'مبلغ سفارش' }}
                </div>
                <div class="flex flex-row-reverse items-center space-x-reverse space-x-2">
                    <span class="text-lg font-bold font-mono" [ngClass]="amountColorClass">{{ data.amount }}</span>
                    <span class="text-sm text-slate-500 dark:text-slate-400">{{ data.currency }}</span>
                </div>
            </div>
            <div class="text-left">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">زمان</div>
                <div class="text-sm font-mono text-slate-600 dark:text-slate-300">
                    {{ data.timestamp | date:"yyyy/MM/dd, HH:mm" }}
                </div>
            </div>
        </div>

        <!-- Discount Section (for orders only) -->
        <div class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-3 border border-amber-200 dark:border-amber-800"
            *ngIf="data.showDiscount && rowType === 'order'">
            <div class="flex flex-row-reverse items-center justify-between">
                <div class="text-right">
                    <div class="text-xs text-amber-700 dark:text-amber-300 mb-1">اعتبار شارژ شده</div>
                    <div class="flex flex-row-reverse items-center space-x-reverse space-x-2">
                        <span class="text-lg font-bold text-emerald-600 dark:text-emerald-400 font-mono">{{ data.amount
                            }}</span>
                        <span class="text-sm text-amber-600 dark:text-amber-400">{{ data.currency }}</span>
                    </div>
                </div>
                <div class="text-left">
                    <div class="text-xs text-amber-700 dark:text-amber-300 mb-1">درصد تخفیف</div>
                    <div class="flex items-center space-x-1 font-mono">
                        <span class="text-lg font-bold text-amber-600 dark:text-amber-400">{{ data.discountPercent
                            }}</span>
                        <span class="text-sm text-amber-600 dark:text-amber-400">%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Details -->
        <div class="grid grid-cols-2 gap-4" *ngIf="data.tertiaryInfo">
            <div class="text-right" *ngIf="data.quaternaryInfo">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">{{ data.quaternaryInfo.label }}</div>
                <div class="text-sm font-medium font-mono"
                    [ngClass]="data.quaternaryInfo.colorClass || 'text-slate-700 dark:text-slate-300'">
                    {{ data.quaternaryInfo.value }}
                </div>
            </div>
            <div class="text-left">
                <div class="text-xs text-slate-500 dark:text-slate-400 mb-1">{{ data.tertiaryInfo.label }}</div>
                <div class="text-sm font-medium"
                    [ngClass]="data.tertiaryInfo.colorClass || 'text-slate-700 dark:text-slate-300'">
                    {{ data.tertiaryInfo.value }}
                </div>
            </div>
        </div>

        <!-- Status Message -->
        <div class="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
            @if(rowType==='payment'){
            <a [routerLink]="['/payment/details', data.primaryId]" class="flex space-x-reverse space-x-2 items-center justify-center w-full" [ngClass]="statusColorClass">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                    <path fill-rule="evenodd"
                        d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                        clip-rule="evenodd" />
                </svg>
                <div class="text-center text-sm font-medium">
                    <span>مشاهده جزئیات و فاکتور</span>
                </div>
            </a>

            } @else {
            <div class="text-center text-sm font-medium" [ngClass]="statusColorClass">
                <span>{{ statusText }}</span>
            </div>
            }
        </div>
    </div>
</div>