/* Enhanced History Row Styles */

.status-badge {
    transition: all 0.2s ease-in-out;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-icon svg {
    transition: transform 0.2s ease-in-out;
}

.status-badge:hover .status-icon svg {
    transform: scale(1.1);
}

/* Pulse animation for pending status */
.status-badge.bg-amber-100 {
    animation: pulse-amber 2s infinite;
}

@keyframes pulse-amber {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Success status glow effect */
.status-badge.bg-emerald-100 {
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

/* Error status subtle shake on hover */
.status-badge.bg-red-100:hover {
    animation: subtle-shake 0.5s ease-in-out;
}

@keyframes subtle-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Enhanced card hover effect */
.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .status-badge.bg-emerald-100 {
        box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
    }

    .content-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
}
