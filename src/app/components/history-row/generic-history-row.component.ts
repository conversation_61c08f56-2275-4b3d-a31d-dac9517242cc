import { Component, Input } from '@angular/core';
import { OrderStatus } from '../../models/order.dto';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

export interface HistoryRowData {
  primaryId: string;
  primaryLabel: string;
  timestamp: number;
  amount: string;
  currency: string;
  secondaryInfo?: {
    label: string;
    value: string;
    colorClass?: string;
  };
  tertiaryInfo?: {
    label: string;
    value: string;
    colorClass?: string;
  };
  quaternaryInfo?: {
    label: string;
    value: string;
    colorClass?: string;
  };
  status: OrderStatus;
  statusText: {
    success: string;
    failure: string;
  };
  showDiscount?: boolean;
  discountPercent?: number;
}

export type HistoryRowType = 'order' | 'payment';

@Component({
  selector: 'app-generic-history-row',
  templateUrl: './generic-history-row.component.html',
  styleUrl: './generic-history-row.component.css'
})
export class GenericHistoryRowComponent {
  @Input() rowType: HistoryRowType = 'order';
  @Input() data!: HistoryRowData;

  constructor(private sanitizer: DomSanitizer) {}

  get statusColorClass(): string {
    return this.data.status === 'PAYMENT_SUCCESS' 
      ? 'text-lime-600 dark:text-lime-400' 
      : 'text-rose-600 dark:text-rose-500';
  }

  get statusText(): string {
    return this.data.status === 'PAYMENT_SUCCESS' 
      ? this.data.statusText.success 
      : this.data.statusText.failure;
  }

  get amountColorClass(): string {
    if (this.rowType === 'payment') {
      return 'text-green-600 dark:text-green-400';
    }
    return this.data.status === 'PAYMENT_SUCCESS'
      ? 'text-green-600 dark:text-green-400 text-lg'
      : 'text-slate-700 dark:text-slate-100';
  }

  get statusBorderClass(): string {
    switch (this.data.status) {
      case 'PAYMENT_SUCCESS':
      case 'FULFILLED':
        return 'border-l-emerald-500';
      case 'PAYMENT_PENDING':
        return 'border-l-amber-500';
      case 'PAYMENT_FAILED':
      case 'UNHANDLED_ERROR':
        return 'border-l-red-500';
      case 'PAYMENT_CANCELED':
      case 'USER_CANCELED':
        return 'border-l-gray-500';
      default:
        return 'border-l-slate-300';
    }
  }

  get statusBadgeClass(): string {
    switch (this.data.status) {
      case 'PAYMENT_SUCCESS':
      case 'FULFILLED':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'PAYMENT_PENDING':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'PAYMENT_FAILED':
      case 'UNHANDLED_ERROR':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      case 'PAYMENT_CANCELED':
      case 'USER_CANCELED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 px-3 py-1.5 rounded-full flex flex-row-reverse items-center space-x-reverse space-x-2';
    }
  }

  get statusIcon(): SafeHtml {
    let iconSvg = '';
    switch (this.data.status) {
      case 'PAYMENT_SUCCESS':
      case 'FULFILLED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        break;
      case 'PAYMENT_PENDING':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>';
        break;
      case 'PAYMENT_FAILED':
      case 'UNHANDLED_ERROR':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
        break;
      case 'PAYMENT_CANCELED':
      case 'USER_CANCELED':
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path></svg>';
        break;
      default:
        iconSvg = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
    }
    return this.sanitizer.bypassSecurityTrustHtml(iconSvg);
  }
}
