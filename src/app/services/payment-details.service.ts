import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, delay } from 'rxjs';
import { environment } from '../../environments/environment.development';
import { ResponseDTO } from '../models/response.dto';
import { PaymentDetails, Invoice, SamplePaymentDetails, SampleInvoice } from '../models/payment-details.model';

@Injectable({
  providedIn: 'root'
})
export class PaymentDetailsService {

  constructor(private http: HttpClient) { }

  /**
   * Get payment details by payment ID
   * @param paymentId The payment ID to fetch details for
   * @returns Observable of payment details response
   */
  public getPaymentDetails(paymentId: string): Observable<ResponseDTO<PaymentDetails>> {
    // For development, return sample data
    if (!environment.IsProduction) {
      return of({
        error: false,
        message: 'Payment details retrieved successfully',
        results: 1,
        result: {
          ...SamplePaymentDetails,
          paymentId: paymentId
        }
      }).pipe(delay(1000)); // Simulate network delay
    }

    // Production API call
    return this.http.get<ResponseDTO<PaymentDetails>>(
      `${environment.ApiBaseURL}/payment/details/${paymentId}`
    );
  }

  /**
   * Get invoice data for a payment
   * @param paymentId The payment ID to fetch invoice for
   * @returns Observable of invoice response
   */
  public getInvoice(paymentId: string): Observable<ResponseDTO<Invoice>> {
    // For development, return sample data
    if (!environment.IsProduction) {
      return of({
        error: false,
        message: 'Invoice retrieved successfully',
        results: 1,
        result: {
          ...SampleInvoice,
          paymentId: paymentId
        }
      }).pipe(delay(1500)); // Simulate network delay
    }

    // Production API call
    return this.http.get<ResponseDTO<Invoice>>(
      `${environment.ApiBaseURL}/payment/invoice/${paymentId}`
    );
  }

  /**
   * Download invoice as PDF
   * @param paymentId The payment ID to download invoice for
   * @returns Observable of blob response
   */
  public downloadInvoicePDF(paymentId: string): Observable<Blob> {
    return this.http.get(
      `${environment.ApiBaseURL}/payment/invoice/${paymentId}/pdf`,
      { responseType: 'blob' }
    );
  }

  /**
   * Send invoice via email
   * @param paymentId The payment ID
   * @param email The email address to send to
   * @returns Observable of response
   */
  public sendInvoiceEmail(paymentId: string, email: string): Observable<ResponseDTO<boolean>> {
    return this.http.post<ResponseDTO<boolean>>(
      `${environment.ApiBaseURL}/payment/invoice/${paymentId}/email`,
      { email }
    );
  }

  /**
   * Get payment history for current user
   * @param page Page number for pagination
   * @param limit Number of items per page
   * @returns Observable of payment history response
   */
  public getPaymentHistory(page: number = 1, limit: number = 10): Observable<ResponseDTO<PaymentDetails[]>> {
    const params = { page: page.toString(), limit: limit.toString() };
    
    return this.http.get<ResponseDTO<PaymentDetails[]>>(
      `${environment.ApiBaseURL}/payment/history`,
      { params }
    );
  }

  /**
   * Search payments by criteria
   * @param criteria Search criteria
   * @returns Observable of search results
   */
  public searchPayments(criteria: PaymentSearchCriteria): Observable<ResponseDTO<PaymentDetails[]>> {
    return this.http.post<ResponseDTO<PaymentDetails[]>>(
      `${environment.ApiBaseURL}/payment/search`,
      criteria
    );
  }

  /**
   * Get payment statistics
   * @param period Time period for statistics
   * @returns Observable of payment statistics
   */
  public getPaymentStatistics(period: 'week' | 'month' | 'year'): Observable<ResponseDTO<PaymentStatistics>> {
    return this.http.get<ResponseDTO<PaymentStatistics>>(
      `${environment.ApiBaseURL}/payment/statistics`,
      { params: { period } }
    );
  }
}

// Additional interfaces for extended functionality
export interface PaymentSearchCriteria {
  paymentId?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
  paymentMethod?: string;
  merchantName?: string;
}

export interface PaymentStatistics {
  totalPayments: number;
  totalAmount: number;
  successfulPayments: number;
  failedPayments: number;
  averageAmount: number;
  topMerchants: Array<{
    name: string;
    count: number;
    amount: number;
  }>;
  paymentMethods: Array<{
    method: string;
    count: number;
    percentage: number;
  }>;
}
